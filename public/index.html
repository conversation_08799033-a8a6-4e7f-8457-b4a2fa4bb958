<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/png" href="favicon.ico">
    <title>GoFreight - A professional truck transportation platform</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            padding: 20px 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            height: 40px;
            width: auto;
        }

        .logo-text {
            color: #333;
            font-size: 28px;
            font-weight: bold;
        }

        /* Main content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px;
            max-width: 600px;
            margin: 0 auto;
        }

        .main-title {
            color: #2c7a7b;
            font-size: 64px;
            font-weight: bold;
            margin-bottom: 30px;
            line-height: 1.1;
        }

        .subtitle {
            color: #4a5568;
            font-size: 20px;
            margin-bottom: 60px;
            line-height: 1.4;
            max-width: 500px;
        }

        .download-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 60px;
            width: 100%;
            max-width: 400px;
        }

        .download-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 500;
            transition: transform 0.3s, box-shadow 0.3s;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-height: 60px;
        }

        .download-btn.google {
            background: #333;
            color: white;
        }

        .download-btn.apple {
            background: #2c7a7b;
            color: white;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .download-btn img {
            width: 24px;
            height: 24px;
        }

        /* QR Codes Section */
        .qr-section {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-bottom: 40px;
        }

        .qr-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .qr-title {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .qr-subtitle {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
        }

        .qr-code {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 30px 20px;
        }

        .footer p {
            color: #718096;
            font-size: 14px;
            margin: 0;
        }

        /* Desktop responsive */
        @media (min-width: 769px) {
            .download-buttons {
                flex-direction: row;
                justify-content: center;
                max-width: 600px;
            }

            .download-btn {
                flex: 1;
                max-width: 280px;
            }

            .qr-section {
                gap: 80px;
            }
        }

        /* Tablet and mobile responsive */
        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
            }

            .main-content {
                padding: 20px;
            }

            .main-title {
                font-size: 48px;
            }

            .subtitle {
                font-size: 18px;
                margin-bottom: 40px;
            }

            .download-buttons {
                margin-bottom: 40px;
            }

            .qr-section {
                flex-direction: column;
                gap: 30px;
                align-items: center;
            }

            .qr-code {
                width: 100px;
                height: 100px;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 36px;
            }

            .subtitle {
                font-size: 16px;
            }

            .download-btn {
                padding: 14px 24px;
                font-size: 16px;
            }

            .logo {
                height: 32px;
            }

            .logo-text {
                font-size: 24px;
            }
        }

        /* Ensure no vertical scrolling */
        @media (max-height: 700px) {
            .main-content {
                padding: 20px;
            }

            .main-title {
                font-size: 48px;
                margin-bottom: 20px;
            }

            .subtitle {
                font-size: 16px;
                margin-bottom: 30px;
            }

            .download-buttons {
                margin-bottom: 30px;
            }

            .qr-section {
                gap: 40px;
                margin-bottom: 20px;
            }

            .qr-code {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo-container">
            <img src="images/logo.png" alt="GoFreight Logo" class="logo">
            <span class="logo-text">GoFreight</span>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <h1 class="main-title">GoFreight</h1>
        <p class="subtitle">A professional truck transportation platform<br>focusing on Central Asia, Russia and Europe.</p>

        <div class="download-buttons">
            <a href="#" class="download-btn google">
                <img src="images/google.png" alt="Google Play">
                Google Play
            </a>
            <a href="#" class="download-btn apple">
                <img src="images/apple.png" alt="Apple Store">
                apple store
            </a>
        </div>

        <!-- QR Codes Section -->
        <div class="qr-section">
            <div class="qr-item">
                <div class="qr-title">Wechat</div>
                <div class="qr-subtitle">Mini Program</div>
                <img src="images/wechatCode.jpg" alt="WeChat Mini Program QR Code" class="qr-code">
            </div>
            <div class="qr-item">
                <div class="qr-title">Contact us via</div>
                <div class="qr-subtitle">WhatsApp</div>
                <img src="images/qrcode.png" alt="WhatsApp Contact QR Code" class="qr-code">
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <p>Copyright © 2025 GoFreight</p>
    </footer>
</body>
</html>
