<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/png" href="favicon.ico">
    <title>GoFreight - 专注中国-中亚货物运输</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            background-image: url('images/background.jpg');
            background-size: cover;
            background-position: center;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: relative;
            z-index: 10;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo {
            height: 16px;
        }

        .logo-text {
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .language-menu {
            display: flex;
            gap: 20px;
        }

        .language-menu a {
            color: white;
            text-decoration: none;
            font-size: 16px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        .language-menu a:hover,
        .language-menu a.active {
            opacity: 1;
        }

        /* Main content */
        .main-content {
            position: relative;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 200px);
            text-align: center;
            padding: 0 40px;
        }

        .main-title {
            color: white;
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .subtitle {
            color: white;
            font-size: 24px;
            margin-bottom: 60px;
            opacity: 0.9;
        }

        .download-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 40px;
        }

        .download-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            color: #333;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            transition: transform 0.3s, box-shadow 0.3s;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .download-btn img {
            width: 24px;
            height: 24px;
        }

        /* QR Code */
        .qr-container {
            position: fixed;
            bottom: 40px;
            right: 40px;
            z-index: 10;
            width: 30vh;
            height: 30vh;
        }

        .qr-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .qr-code {
            width: 120px;
            height: 120px;
            margin-bottom: 10px;
        }

        .qr-text {
            font-size: 14px;
            color: #666;
        }

        /* Footer */
        .footer {
            position: relative;
            z-index: 10;
            text-align: center;
            padding: 30px 20px;
            margin-top: 60px;
        }

        .footer p {
            color: white;
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
                flex-direction: column;
                gap: 15px;
            }

            .main-content {
                padding: 0 20px;
            }

            .main-title {
                font-size: 48px;
            }

            .subtitle {
                font-size: 18px;
            }

            .download-buttons {
                flex-direction: column;
                align-items: center;
            }

            .qr-container {
                bottom: 20px;
                right: 20px;
                padding: 15px;
            }

            .qr-code {
                width: 100px;
                height: 100px;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 36px;
            }

            .subtitle {
                font-size: 16px;
            }

            .download-btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo-container">
            <img src="images/logo.png" alt="GoFreight Logo" class="logo">
        </div>
        <!-- <nav class="language-menu">
            <a href="#" class="active">中文</a>
            <a href="#">Русский</a>
            <a href="#">English</a>
        </nav> -->
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <h1 class="main-title">GoFreight</h1>
        <p class="subtitle">专注中国-中亚货物运输</p>

        <div class="download-buttons">
            <a href="#" class="download-btn">
                <img src="images/google.png" alt="Google Play">
                Google Play 安装
            </a>
            <a href="#" class="download-btn">
                <img src="images/apple.png" alt="Apple Store">
                Apple Store 安装
            </a>
        </div>
    </main>

    <!-- QR Code -->
    <div class="qr-container">
        <img src="images/qrcode.png" alt=""></img>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <p>Copyright © 2025 GoFreight</p>
    </footer>
</body>
</html>
