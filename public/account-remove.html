<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Deletion Request - GoFreight</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #029FB0, #0288a3);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .description {
            margin-bottom: 30px;
            color: #666;
            line-height: 1.8;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #029FB0;
        }

        .submit-btn {
            width: 100%;
            padding: 14px;
            background: #029FB0;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 10px;
        }

        .submit-btn:hover {
            background: #0288a3;
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #029FB0;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success {
            display: none;
            text-align: center;
            padding: 40px 20px;
        }

        .success-icon {
            width: 60px;
            height: 60px;
            background: #28a745;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .success h2 {
            color: #28a745;
            margin-bottom: 15px;
        }

        .success p {
            color: #666;
            line-height: 1.6;
        }

        .data-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-size: 14px;
            color: #666;
        }

        .data-info h3 {
            color: #029FB0;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.6em;
            }

            .content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Account Deletion Request</h1>
            <p>GoFreight App</p>
        </div>

        <div class="content">
            <div id="form-section">
                <div class="description">
                    <p>According to user data protection regulations, you have the right to request deletion of your personal account and related data in the GoFreight app. Please enter your account email address below, and we will process your deletion request within 30 days.</p>
                </div>

                <div class="data-info">
                    <h3>Data Processing Information</h3>
                    <p><strong>Data to be deleted:</strong> Personal identity information, user profile, location data, device information</p>
                </div>

                <form id="deletion-form">
                    <div class="form-group">
                        <label for="email">Account Email Address</label>
                        <input type="email" id="email" name="email" required placeholder="Please enter your email address">
                    </div>

                    <button type="submit" class="submit-btn" id="submit-btn">Submit Deletion Request</button>
                </form>
            </div>

            <div id="loading-section" class="loading">
                <div class="loading-spinner"></div>
                <p>Processing your request, please wait...</p>
            </div>

            <div id="success-section" class="success">
                <div class="success-icon">✓</div>
                <h2>Request Submitted Successfully</h2>
                <p>We have received your account deletion request. We will complete the processing within 30 days.</p>
                <p style="margin-top: 15px; font-size: 14px;">If you have any questions, please contact customer service: <EMAIL></p>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 GoFreight. All rights reserved.</p>
        </div>
    </div>

    <script>
        document.getElementById('deletion-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            if (!email) {
                alert('Please enter a valid email address');
                return;
            }

            // 隐藏表单，显示加载状态
            document.getElementById('form-section').style.display = 'none';
            document.getElementById('loading-section').style.display = 'block';

            // 2秒后显示成功页面
            setTimeout(function() {
                document.getElementById('loading-section').style.display = 'none';
                document.getElementById('success-section').style.display = 'block';
            }, 2000);
        });
    </script>
</body>
</html>