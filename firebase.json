{"flutter": {"platforms": {"android": {"default": {"projectId": "mashina-driver", "appId": "1:575567691157:android:a25dac843690fbeed14a9c", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "mashina-driver", "appId": "1:575567691157:ios:4a78e079d4bffe7ad14a9c", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "mashina-driver", "configurations": {"android": "1:575567691157:android:a25dac843690fbeed14a9c", "ios": "1:575567691157:ios:4a78e079d4bffe7ad14a9c"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "ui": {"enabled": true}, "singleProjectMode": true, "pubsub": {"port": 8085}}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}}