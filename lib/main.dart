import 'dart:async';

import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

import 'firebase_options.dart';
import 'src/app.dart';

void main() async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();
    await Device.shared.init();
    await LSApp.shared
        .initFirebase(DefaultFirebaseOptions.currentPlatform, true);
    // Api.init();

    // final appLinks = AppLinks();
    // appLinks.uriLinkStream.listen((uri) {
    //   if (uri.host == Api.appHost &&
    //       uri.queryParameters['mode'] == 'verifyEmail') {
    //     Future.delayed(
    //         const Duration(milliseconds: 200),
    //             () => WebView.openUrl(
    //             MyApp.materialKey.currentContext!, uri.toString()));
    //   }
    // });

    runApp(const MyApp());
  }, (error, stackTrace) {
    debugPrint('$error ${stackTrace.toString()}');
    GA.recordError(error, stackTrace);
  });
}
