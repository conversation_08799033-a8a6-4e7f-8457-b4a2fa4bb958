import 'dart:convert';

import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:flutter/material.dart';
import 'package:driver/src/model/firestore.dart' as fs;
import 'package:driver/src/utils/firestore.dart' as fsu;
import 'package:driver/src/model/notification.dart' as notif;
import 'package:lsapp/lsapp.dart';

class NotificationListView extends StatefulWidget {
  const NotificationListView({super.key});

  static const String routeName = 'notifications';

  @override
  State<StatefulWidget> createState() => _NotificationListViewState();
}

class _NotificationListViewState extends State<NotificationListView>
    with PageMixin, EntryMixin {
  @override
  void initState() {
    super.initState();
    notif.Notification.shared.update();
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(appLoc(context).notifications,
        notificationProvider((notif) {
      return Column(
          spacing: 10,
          children: notif.notifications.map((e) => item(e)).toList());
    }), actions: [
      Padding(
          padding: EdgeInsets.only(right: 10),
          child: IconButton(
              onPressed: () async {
                Loader.show();
                await notif.Notification.shared.markAllRead();
                Loader.hide();
              },
              icon: Icon(Icons.clear_all)))
    ]);
  }

  Widget item(fs.Notification notification) {
    return InkWell(
        onTap: () => onTap(notification),
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(4)),
            padding: EdgeInsets.all(10),
            width: MediaQuery.of(context).size.width,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(children: [
                Stack(children: [
                  Icon(Icons.notifications_none, size: 24),
                  if (notification.unread)
                    Positioned(
                        top: 2,
                        right: 4,
                        child: Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                              color: Color(0xFFF2242B),
                              borderRadius: BorderRadius.circular(6)),
                        ))
                ]),
                Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Text(notification.title,
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 16))),
                Spacer(),
                Text(notification.created.toMinuteString(),
                    style: TextStyle(color: Color(0xFF666666), fontSize: 14))
              ]),
              Padding(
                  padding: EdgeInsets.only(top: 12, left: 32),
                  child: body(notification))
            ])));
  }

  Widget body(fs.Notification notification) {
    if (notification.type == 'orderPayment') {
      return RichText(
          text: TextSpan(
              style: TextStyle(color: Color(0xFF666666), fontSize: 14),
              text:
                  notification.body.replaceAll(appLoc(context).viewPayment, ""),
              children: [
            TextSpan(
                text: appLoc(context).viewPayment,
                style: TextStyle(
                    color: primaryColor,
                    fontSize: 14,
                    decoration: TextDecoration.underline))
          ]));
    }

    return Text(notification.body,
        style: TextStyle(color: Color(0xFF666666), fontSize: 14));
  }

  Future onTap(fs.Notification notification) async {
    notif.Notification.shared.markRead(notification);
    if (notification.type == 'orderPayment' &&
        notification.arguments is String) {
      try {
        final args = jsonDecode(notification.arguments as String);
        Map<String, dynamic>? data =
            await fsu.Firestore.shared.getPayment(args['id']);
        if (data?['paymentImgStorage'] != null) {
          showImagePreview(data!['paymentImgStorage']);
        } else if (mounted) {
          Loader.show(loadingText: appLoc(context).loadingImage);
          final path = await Functions.shared.getStorageImage(args['id'],
              args['token'], args['recordId'], 'getPaymentProofFromLark');
          Loader.hide();
          if (path != null) {
            showImagePreview(path);
          }
        }
      } catch (e) {
        debugPrint('$e');
      }
    } else if (notification.routeName?.isNotEmpty == true) {
      Navigator.of(context).pushReplacementNamed(notification.routeName!,
          arguments: notification.arguments);
    }
  }
}
