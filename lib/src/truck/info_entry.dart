import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/dropdown.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/truck/license_entry.dart';
import 'package:driver/src/truck/view.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/utils/firestore.dart' as fs;

class TruckInfoEntryView extends StatefulWidget {
  const TruckInfoEntryView({super.key});

  static const String routeName = 'truck_info_entry';

  @override
  State<StatefulWidget> createState() => _TruckInfoEntryViewState();
}

class _TruckInfoEntryViewState extends State<TruckInfoEntryView>
    with UnfocusMixin, EntryMixin, PageMixin {
  String? _selectedType;
  List<DropdownMenuEntry<TonnageLabel>> _tonnageEntries = [];
  TonnageLabel? _selectedTonnage;
  List<DropdownMenuEntry<VolumeLabel>> _volumeEntries = [];
  VolumeLabel? _selectedVolume;
  late TruckLicenseEntry _licenseEntry;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        List<TonnageLabel> tonnages = SettingsController.shared.tonnages;
        _tonnageEntries = tonnages
            .map((label) => DropdownMenuEntry<TonnageLabel>(
                value: label, label: label.localizedLabel()))
            .toList();
        List<VolumeLabel> volumes = SettingsController.shared.volumes;
        _volumeEntries = volumes
            .map((label) => DropdownMenuEntry<VolumeLabel>(
                value: label, label: label.localizedLabel()))
            .toList();
        _licenseEntry =
            ModalRoute.of(context)!.settings.arguments as TruckLicenseEntry;
        _selectedType = _licenseEntry.truck?.type;
        _selectedTonnage = tonnages
            .where((e) => e.tonnage == _licenseEntry.truck?.tonnage)
            .firstOrNull;
        _selectedVolume = volumes
            .where((e) => e.volume == _licenseEntry.truck?.volume)
            .firstOrNull;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return unfocusContainer(pageBase(appLoc(context).addTruckTitle,
        Column(children: [type(), tonnage(), volume()]),
        operation: confirmButton()));
  }

  Widget type() {
    int cols = 4;
    double width = (MediaQuery.of(context).size.width - 80) / cols;
    double childAspectRatio = 1;
    if (width < 90 || appLoc(context).localeName != 'zh') {
      childAspectRatio = 0.8;
    }
    double height = width * 130 / 172;
    return formItem(
        title: appLoc(context).truckType,
        isRequired: true,
        input: GridView.count(
            padding: EdgeInsets.only(top: 10),
            crossAxisCount: cols,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            childAspectRatio: childAspectRatio,
            children: SettingsController.shared.truckTypes
                .map((e) => InkWell(
                    onTap: () {
                      setState(() {
                        _selectedType = e.name;
                      });
                    },
                    child: Column(
                      children: [
                        Image.asset(
                            truckImage(e.name,
                                isSelected: _selectedType == e.name),
                            width: width,
                            height: height),
                        Text(e.localizedLabel(), style: TextStyle(fontSize: 12))
                      ],
                    )))
                .toList()));
  }

  Widget tonnage() {
    return formItem(
        title: appLoc(context).tonnage,
        isRequired: true,
        input: dropDown(_tonnageEntries,
            hintText: appLoc(context).tonnagePlaceholder,
            initialSelection: _selectedTonnage, onSelected: (value) {
          setState(() {
            _selectedTonnage = value;
          });
        }));
  }

  Widget volume() {
    return formItem(
        title: appLoc(context).volume,
        isRequired: true,
        input: dropDown(_volumeEntries,
            hintText: appLoc(context).volumePlaceholder,
            initialSelection: _selectedVolume, onSelected: (value) {
          setState(() {
            _selectedVolume = value;
          });
        }));
  }

  BottomOperationPanel confirmButton() {
    saveInfo() async {
      Loader.show();
      final ret = await fs.Firestore.shared.createTruckVerification(
          license: _licenseEntry.license,
          licenseStorage: _licenseEntry.licenseStorage,
          truck: _licenseEntry.truck,
          type: _selectedType!,
          tonnage: _selectedTonnage!.tonnage,
          volume: _selectedVolume!.volume);
      Loader.hide();
      if (ret != null) {
        User.shared.updateTruckVerification(ret);
        if (mounted) {
          await showAppDialog(
              context,
              dialogMessage(
                  message: appLoc(context).waitForTruckVerificationMessage),
              [
                dialogButton(context, appLoc(context).understand,
                    DialogButtonType.primary)
              ],
              title: appLoc(context).waitForDriverVerificationTitle,
              barrierDismissible: false);
          if (mounted) {
            Navigator.of(context)
                .popUntil(ModalRoute.withName(TruckManagementView.routeName));
          }
        }
      } else if (mounted) {
        showSnackBar(context,
            type: SnackType.error, text: appLoc(context).networkError);
      }
    }

    return BottomOperationPanel.singleOperation(
        context,
        appLoc(context).confirm,
        _selectedType != null &&
                _selectedVolume != null &&
                _selectedTonnage != null
            ? saveInfo
            : null);
  }
}
