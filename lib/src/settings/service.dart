import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class SettingsService extends LSSettingsService {
  static const String firstLaunchKey = '9dd43dc2f71d96b3123956faa1724de1';
  static const String translationsKey = '518510647930197ae5b7329a1b36dcf2';
  static const String originsKey = 'bf353d41089fe8caa6b6cea5f6c3fe71';
  static const String destinationCountriesKey =
      'a6088285df1eeb8d5c2a70d6b830e491';
  static const String destinationCitiesKey = '000066c0d7be56c14f5468fd164f8731';
  static const String quoteCitiesKey = "55d69dfb427a4168c77fc2f1edd3f482";
  static const String quoteTruckTypesKey = "44b6960a09a1d6187b8b97d1e7ab64e1";
  static const String quoteCitiesInHomePageKey =
      "c5ed7dbe1f528ce983a3b4a6989fa7b1";
  static const String truckTypesKey = "b06296340f782f62cee1d3d825175701";
  static const String countryAreaCodesKey = '22d1bf775f4ddb0e2cb6a444b22d4c01';
  static const String tonnagesKey = '11febacf090bdea939c706c922bdef71';
  static const String volumesKey = '2d2dc31c6b213fc44aa3be9eb5ccfdf1';
  static const String defaultTruckIndexKey = '566d5732f96171b3544ae32ee0070ee3';
  static const String selectedDestCountryKey = '11b315ba363caa37aad5cb2d7a38f1c2';
  static const String customerServiceKey = '970390340f05782c6d3025893dc28fbf';
  static const String localeKey = '1a3d90b503d11955ac8ddb5edaab1cad';

  Future<ThemeMode> themeMode() async => ThemeMode.system;
}
