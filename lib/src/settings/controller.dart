import 'dart:convert';

import 'package:driver/src/app.dart';
import 'package:driver/src/model/dropdown.dart';
import 'package:driver/src/settings/service.dart';
import 'package:driver/src/settings/translation.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class SettingsController with ChangeNotifier {
  SettingsController(this._settingsService);

  final SettingsService _settingsService;

  static final SettingsController _shared =
      SettingsController(SettingsService());

  static SettingsController get shared => _shared;

  ThemeMode themeMode = ThemeMode.light;
  bool isFirstLaunch = false;
  Map<String, Map<String, String>> translations = {};
  List<Map<String, String>> origins = [];
  List<String> destinationCountries = [];
  Map<String, List<String>> destinationCities = {};
  List<String> quoteCities = [];
  List<String> quoteTruckTypes = [];
  int quoteCitiesInHomePage = 3;
  List<TruckType> truckTypes = [];
  List<CountryCode> countryAreaCodes = [];
  List<TonnageLabel> tonnages = [];
  List<VolumeLabel> volumes = [];
  int defaultTruckIndex = 0;
  String selectedDestCountry = '';
  Map<String, String> customerService = {};
  Locale? locale;
  bool allowGuestAccess = false;
  Map<String, dynamic> guestAccessCred = {};

  Future<void> loadSettings() async {
    themeMode = await _settingsService.themeMode();
    isFirstLaunch = await LSSettingsService.boolValue(
        SettingsService.firstLaunchKey,
        defaultValue: true);
    translations = await mapFromStoredJson(
        SettingsService.translationsKey, Map<String, String>.from,
        defaultValue: defaultTranslation);
    origins = await listFromStoredJson(SettingsService.originsKey,
        fromMap: Map<String, String>.from);
    destinationCountries =
        await listFromStoredJson(SettingsService.destinationCountriesKey);
    destinationCities = await mapFromStoredJson(
        SettingsService.destinationCitiesKey, List<String>.from);
    quoteCities = await listFromStoredJson(SettingsService.quoteCitiesKey);
    quoteTruckTypes =
        await listFromStoredJson(SettingsService.quoteTruckTypesKey);
    quoteCitiesInHomePage = await LSSettingsService.intValue(
        SettingsService.quoteCitiesInHomePageKey,
        defaultValue: 3);
    truckTypes = await listFromStoredJson(SettingsService.truckTypesKey,
        fromMap: TruckType.fromMap);
    countryAreaCodes = await listFromStoredJson(
        SettingsService.countryAreaCodesKey,
        fromMap: CountryCode.fromMap);
    tonnages = await listFromStoredJson(SettingsService.tonnagesKey,
        fromMap: TonnageLabel.new);
    volumes = await listFromStoredJson(SettingsService.volumesKey,
        fromMap: VolumeLabel.new);
    defaultTruckIndex = await LSSettingsService.intValue(
        SettingsService.defaultTruckIndexKey,
        defaultValue: 0);
    selectedDestCountry = await LSSettingsService.stringValue(
        SettingsService.selectedDestCountryKey);
    customerService = await mapFromStoredJson(
        SettingsService.customerServiceKey, (String v) => v);
    String lang =
        await LSSettingsService.stringValue(SettingsService.localeKey);
    if (lang.isNotEmpty) {
      locale = Locale(lang);
    }
    notifyListeners();
  }

  Future<void> updateConfig(FirebaseRemoteConfig config) async {
    translations = await mapFromRemoteConfig(
        config,
        'translations',
        SettingsService.translationsKey,
        translations,
        Map<String, String>.from);
    origins = await listFromRemoteConfig(
        config, 'origins', SettingsService.originsKey, origins,
        fromMap: Map<String, String>.from);
    destinationCountries = await listFromRemoteConfig(
        config,
        'destinationCountries',
        SettingsService.destinationCountriesKey,
        destinationCountries);
    destinationCities = await mapFromRemoteConfig(
        config,
        'destinationCities',
        SettingsService.destinationCitiesKey,
        destinationCities,
        List<String>.from);
    quoteCities = await listFromRemoteConfig(
        config, 'quoteCities', SettingsService.quoteCitiesKey, quoteCities);
    quoteTruckTypes = await listFromRemoteConfig(config, 'quoteTruckTypes',
        SettingsService.quoteTruckTypesKey, quoteTruckTypes);
    quoteCitiesInHomePage = config.getInt('quoteCitiesInHomePage');
    await LSSettingsService.updateIntValue(
        SettingsService.quoteCitiesInHomePageKey, quoteCitiesInHomePage);
    truckTypes = await listFromRemoteConfig(
        config, 'truckTypes', SettingsService.truckTypesKey, truckTypes,
        fromMap: TruckType.fromMap);
    countryAreaCodes = await listFromRemoteConfig(config, 'countryAreaCodes',
        SettingsService.countryAreaCodesKey, countryAreaCodes,
        fromMap: CountryCode.fromMap);
    tonnages = await listFromRemoteConfig(
        config, 'tonnages', SettingsService.tonnagesKey, tonnages,
        fromMap: TonnageLabel.new);
    volumes = await listFromRemoteConfig(
        config, 'volumes', SettingsService.volumesKey, volumes,
        fromMap: VolumeLabel.new);
    customerService = await mapFromRemoteConfig(config, 'customerService',
        SettingsService.customerServiceKey, customerService, (String v) => v);
    allowGuestAccess = config.getBool('allowGuestAccess');
    String json = config.getString('guestAccessCred');
    if (json.isNotEmpty) {
      try {
        guestAccessCred = jsonDecode(json);
      } catch (e) {
        debugPrint('$e');
      }
    }
    notifyListeners();
  }

  static Future<List<T>> listFromStoredJson<T, U>(String serviceKey,
      {T Function(U)? fromMap}) async {
    String json = await LSSettingsService.stringValue(serviceKey);
    if (json.isNotEmpty) {
      if (T == String) {
        return List<T>.from(jsonDecode(json));
      } else {
        return List.from(jsonDecode(json))
            .map((e) => fromMap!(e as U))
            .toList();
      }
    }
    return [];
  }

  static Future<List<T>> listFromRemoteConfig<T, U>(FirebaseRemoteConfig config,
      String key, String serviceKey, List<T> defaultValue,
      {T Function(U)? fromMap}) async {
    String json = config.getString(key);
    if (json.isNotEmpty) {
      await LSSettingsService.updateStringValue(serviceKey, json);
      if (T == String) {
        return List<T>.from(jsonDecode(json));
      } else {
        return List.from(jsonDecode(json))
            .map((e) => fromMap!(e as U))
            .toList();
      }
    }
    return defaultValue;
  }

  static Future<Map<String, T>> mapFromStoredJson<T, U>(
      String serviceKey, T Function(U) fromMap,
      {Map<String, T>? defaultValue}) async {
    String json = await LSSettingsService.stringValue(serviceKey);
    if (json.isNotEmpty) {
      return Map<String, dynamic>.from(jsonDecode(json))
          .map((k, v) => MapEntry(k, fromMap(v)));
    }
    return defaultValue ?? {};
  }

  static Future<Map<String, T>> mapFromRemoteConfig<T, U>(
      FirebaseRemoteConfig config,
      String key,
      String serviceKey,
      Map<String, T> defaultValue,
      T Function(U) fromMap) async {
    String json = config.getString(key);
    if (json.isNotEmpty) {
      await LSSettingsService.updateStringValue(serviceKey, json);
      return Map<String, dynamic>.from(jsonDecode(json))
          .map((k, v) => MapEntry(k, fromMap(v)));
    }
    return defaultValue;
  }

  Future<void> updateFirstLaunch(bool value) async {
    isFirstLaunch = value;
    await LSSettingsService.updateBoolValue(
        SettingsService.firstLaunchKey, value);
    notifyListeners();
  }

  Future<void> updateDefaultTruckIndex(int value) async {
    defaultTruckIndex = value;
    await LSSettingsService.updateIntValue(
        SettingsService.defaultTruckIndexKey, value);
    notifyListeners();
  }

  Future<void> updateSelectedDestCountry(String? value) async {
    selectedDestCountry = value ?? '';
    await LSSettingsService.updateStringValue(
        SettingsService.selectedDestCountryKey, selectedDestCountry);
    notifyListeners();
  }

  Future<void> updateLocale(String value) async {
    locale = Locale(value);
    await LSSettingsService.updateStringValue(SettingsService.localeKey, value);
    notifyListeners();
  }

  String translate(String str) {
    if (translations.containsKey(str)) {
      String localeKey = locale?.languageCode ??
          appLoc(MyApp.materialKey.currentContext!).localeName;
      return translations[str]![localeKey]!;
    }
    return str;
  }
}
