import 'package:driver/src/mixins/order.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/order/logs.dart';
import 'package:driver/src/order/report.dart';
import 'package:driver/src/order/photo_upload.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart' as fs;
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lsapp/lsapp.dart';

class OrderDetailView extends StatefulWidget {
  const OrderDetailView({super.key});

  static const String routeName = 'order_detail';

  @override
  State<StatefulWidget> createState() => _OrderDetailViewState();
}

class _OrderDetailViewState extends State<OrderDetailView>
    with PageMixin, OrderMixin {
  Order? _order;
  List<DropdownMenuEntry<TruckVerification>> _availableTrucks = [];
  TruckVerification? _selectedTruck;
  final TextEditingController _quoteController = TextEditingController();
  OrderNegotiation? _negotiation;
  bool _orderLoaded = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final orderId = ModalRoute.of(context)!.settings.arguments as String?;
      if (orderId != null) {
        _order = await fs.Firestore.shared.getOrder(orderId);
        if (_order != null && _order!.status == OrderStatus.matching) {
          await updateNegotiation();
        }

        if (mounted) {
          setState(() {
            _availableTrucks = User.shared.verifiedTrucks
                .where((e) => e.type == _order?.type)
                .map((e) => DropdownMenuEntry(value: e, label: e.label()))
                .toList();
            _selectedTruck = User.shared.defaultTruck();
            _orderLoaded = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(appLoc(context).orderDetail, orderInfo(),
        useTransparentAppBar: true,
        actions: [
          IconButton(
              onPressed: _order == null
                  ? null
                  : () => Navigator.of(context).pushNamed(
                      OrderLogsView.routeName,
                      arguments: _order!.orderId),
              icon: Icon(Icons.library_books_outlined))
        ],
        backgroundHeader: Image.asset('assets/images/order_bg.png',
            width: MediaQuery.of(context).size.width),
        operation: statusPanel());
  }

  BottomOperationPanel? statusPanel() {
    if (!_orderLoaded) {
      return null;
    }

    if (_order != null) {
      switch (_order!.status) {
        case OrderStatus.finding:
          return BottomOperationPanel.twoColumnOperations(
            context,
            title1: appLoc(context).makeQuote,
            onPressed1: _selectedTruck == null ? null : () => takeOrder(true),
            title2: appLoc(context).placeOrder,
            onPressed2: _selectedTruck == null ? null : () => takeOrder(false),
          );
        case OrderStatus.matching:
          if (_negotiation == null) {
            return BottomOperationPanel.twoColumnOperations(
              context,
              title1: appLoc(context).contactService,
              onPressed1: customerService,
              title2: appLoc(context).placeOrder,
              onPressed2:
                  _selectedTruck == null ? null : () => takeOrder(false),
            );
          } else {
            return BottomOperationPanel.singleOperation(
                context, appLoc(context).contactService, customerService);
          }
        case OrderStatus.pendingConfirm:
        case OrderStatus.pendingContract:
        case OrderStatus.completed:
        case OrderStatus.cancelled:
          return BottomOperationPanel.singleOperation(
              context, appLoc(context).contactService, customerService);
        case OrderStatus.pendingLoad:
          if (_order!.loadingStart == null) {
            return BottomOperationPanel.twoColumnOperations(
              context,
              title1: appLoc(context).contactService,
              onPressed1: customerService,
              title2: appLoc(context).weightPhoto,
              onPressed2: () => uploadPhoto(
                  appLoc(context).weightPhoto,
                  appLoc(context).weightPhotoPlaceholder,
                  UploadType.weightPhoto,
                  UpdateOrderFunction.startLoading,
                  'weightPhoto'),
            );
          }
          return BottomOperationPanel.twoColumnOperations(
            context,
            title1: appLoc(context).contactService,
            onPressed1: customerService,
            title2: appLoc(context).finishLoad,
            onPressed2: () async =>
                await callUpdateOrder(UpdateOrderFunction.endLoading),
          );
        case OrderStatus.queueing:
          return BottomOperationPanel.singleOperation(
              context, appLoc(context).activityReport, activityReport);
        case OrderStatus.inTransit:
          return BottomOperationPanel.threeOperations(context,
              title1: appLoc(context).dailyReport,
              onPressed1: () async => await sendActivityReport(
                  _order, ActivityReportType.dailyReport, shouldPop: false),
              title2: appLoc(context).activityReport,
              onPressed2: activityReport,
              title3: appLoc(context).startClearance,
              onPressed3: () async =>
                  await callUpdateOrder(UpdateOrderFunction.startClearance));
        case OrderStatus.inClearance:
          return BottomOperationPanel.twoColumnOperations(context,
              title1: appLoc(context).activityReport,
              onPressed1: activityReport,
              title2: appLoc(context).endClearance,
              onPressed2: () async =>
                  await callUpdateOrder(UpdateOrderFunction.endClearance));
        case OrderStatus.pendingUnload:
          String title2;
          void Function() onPressed2;
          if (_order?.unloadingStart == null) {
            title2 = appLoc(context).arriveDestination;
            onPressed2 =
                () => callUpdateOrder(UpdateOrderFunction.startUnloading);
          } else if (_order?.unloadingEnd == null) {
            title2 = appLoc(context).unloadComplete;
            onPressed2 = () => uploadPhoto(
                appLoc(context).uploadReceipt,
                appLoc(context).uploadReceiptPlaceholder,
                UploadType.receipt,
                UpdateOrderFunction.endUnloading,
                'receipt');
          } else {
            title2 = appLoc(context).customerService;
            onPressed2 = customerService;
          }
          return BottomOperationPanel.twoColumnOperations(context,
              title1: appLoc(context).activityReport,
              onPressed1: activityReport,
              title2: title2,
              onPressed2: onPressed2);
      }
    }

    return null;
  }

  Future callUpdateOrder(UpdateOrderFunction func) async {
    final order = await updateOrder(_order!, func);
    if (order != null) {
      setState(() {
        _order = order;
      });
    }
  }

  Future uploadPhoto(String title, String subtitle, UploadType type,
      UpdateOrderFunction func, String functionArgKey) async {
    final order = await doUploadPhoto(
        _order!, title, subtitle, type, func, functionArgKey);
    if (order != null) {
      setState(() {
        _order = order;
      });
    }
  }

  Future activityReport() async {
    Navigator.of(context)
        .pushNamed(ActivityReportView.routeName, arguments: _order);
  }

  Future updateNegotiation() async {
    final res = await fs.Firestore.shared.getOrderNegotiation(_order!);
    if (mounted) {
      setState(() {
        _negotiation = res;
      });
    }
  }

  String orderSubtitle() {
    if (_order != null) {
      return orderStatusSubtitle(_order!.status,
          hasNegotiation: _negotiation != null);
    }

    return '';
  }

  Widget orderInfo() {
    if (_order == null) {
      return const SizedBox();
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(localizedString(_order!.status),
          style: TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.w500)),
      Container(
          padding: EdgeInsets.only(top: 8),
          width: MediaQuery.of(context).size.width * 0.6,
          child: Text(orderSubtitle(), style: TextStyle(color: Colors.white))),
      detail()
    ]);
  }

  Widget detail() {
    return Container(
      margin: EdgeInsets.only(top: 16),
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(children: [
        Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
              padding: EdgeInsets.only(top: 4, right: 8),
              child: Image.asset(
                  truckImage(_order!.type, ignoreSelection: true),
                  width: 80,
                  fit: BoxFit.fitWidth)),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(localizedString(_order!.type),
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18)),
            const SizedBox(height: 4),
            Text(
                _order!.goodsTranslation?[appLoc(context).localeName] ??
                    _order!.goods,
                style: TextStyle(color: Color(0xFF666666))),
            const SizedBox(height: 4),
            Row(children: [
              if (_order!.tonnage.isNotEmpty)
                Container(
                    margin: EdgeInsets.only(right: 8),
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                        border: Border.all(color: primaryColor.withAlpha(128)),
                        borderRadius: BorderRadius.circular(4)),
                    child: Text(localizedString(_order!.tonnage.first),
                        style: TextStyle(color: primaryColor.withAlpha(128)))),
              if (_order!.volumes.isNotEmpty)
                Container(
                    margin: EdgeInsets.only(right: 8),
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                        border: Border.all(color: primaryColor.withAlpha(128)),
                        borderRadius: BorderRadius.circular(4)),
                    child: Text(localizedString(_order!.volumes.first),
                        style: TextStyle(color: primaryColor.withAlpha(128))))
            ])
          ])
        ]),
        const SizedBox(height: 16),
        row(appLoc(context).orderDistance,
            '${(_order!.distance / 1000).toStringAsFixed(0)}km'),
        row(appLoc(context).orderLoadingAddress,
            '${localizedString(_order!.origin)} ${_order!.pickupAddressTranslation?[appLoc(context).localeName] ?? _order!.pickupAddress}'),
        row(appLoc(context).orderLoadingTime,
            _order!.expectedLoadingDate.toDateString()),
        row(appLoc(context).orderUnloadingAddress,
            '${localizedString(_order!.destinationCountry)} ${localizedString(_order!.destinationCity)} ${_order!.destinationAddressTranslation?[appLoc(context).localeName] ?? _order!.destinationAddress}'),
        row(appLoc(context).orderCost, '\$ ${orderPrice(_order!)}',
            valueColor: errorColor, valueWeight: FontWeight.w500),
        row(appLoc(context).orderId, maskString(_order!.orderId),
            onTap: () async {
          await Clipboard.setData(ClipboardData(text: _order!.orderId));
          if (mounted) {
            showSnackBar(context, text: appLoc(context).copyToClipboard);
          }
        },
            trailing: Padding(
                padding: EdgeInsets.only(left: 8),
                child: Icon(Icons.copy, size: 16))),
      ]),
    );
  }

  Widget row(String title, String value,
      {Color valueColor = const Color(0xFF333333),
      FontWeight valueWeight = FontWeight.w400,
      Widget? trailing,
      void Function()? onTap}) {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 10),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          SizedBox(
              width: MediaQuery.of(context).size.width * 0.35,
              child: Text('$title:',
                  style: TextStyle(color: Color(0xFF666666), fontSize: 16))),
          InkWell(
              onTap: onTap,
              child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                SizedBox(
                    width: MediaQuery.of(context).size.width *
                        (trailing != null ? 0.4 : 0.5),
                    child: Text(value,
                        textAlign: TextAlign.right,
                        style: TextStyle(
                            color: valueColor,
                            fontSize: 16,
                            fontWeight: valueWeight))),
                if (trailing != null) trailing,
              ]))
        ]));
  }

  Future takeOrder(bool makeQuote) async {
    Loader.show();
    if ((await getOnGoingOrders()).isNotEmpty) {
      Loader.hide();
      if (mounted) {
        await showAppDialog(
            context,
            dialogMessage(message: appLoc(context).cannotTakeOrderMessage),
            [
              dialogButton(
                  context, appLoc(context).understand, DialogButtonType.primary)
            ],
            title: appLoc(context).cannotTakeOrderTitle);
      }
      return;
    }
    Loader.hide();
    final position = await getCurrentLocation();
    if (position == null) {
      return;
    }
    if (!mounted) {
      return;
    }

    double width = MediaQuery.of(context).size.width -
        dialogDefaultInsetPadding.left * 2 -
        24 * 2;
    BorderSide borderSide = BorderSide(color: Color(0xFFDFF0F2));
    Color textColor = Color(0xFF666666);
    _quoteController.text = '';
    String title =
        makeQuote ? appLoc(context).makeQuote : appLoc(context).placeOrder;
    String cancelButton = makeQuote
        ? appLoc(context).cancelQuote
        : appLoc(context).cancelPlaceOrder;
    String confirmButton = makeQuote
        ? appLoc(context).confirmQuote
        : appLoc(context).confirmPlaceOrder;
    final res = await showAppDialog(
        context,
        Padding(
            padding: EdgeInsets.only(top: 12, bottom: 24),
            child: StatefulBuilder(builder: (context, setState) {
              return Column(children: [
                Text(
                    '${appLoc(context).orderTargetPrice}: \$${_order!.price()}',
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                const SizedBox(height: 24),
                dropDown(_availableTrucks,
                    initialSelection: _selectedTruck,
                    hintText: appLoc(context).selectYourTruck,
                    width: width, onSelected: (e) {
                  setState(() {
                    _selectedTruck = e;
                  });
                },
                    textStyle: TextStyle(color: textColor, fontSize: 16),
                    inputDecorationTheme: InputDecorationTheme(
                        fillColor: Colors.transparent,
                        filled: true,
                        outlineBorder: BorderSide.none,
                        border: OutlineInputBorder(borderSide: borderSide),
                        focusedBorder:
                            OutlineInputBorder(borderSide: borderSide),
                        enabledBorder:
                            OutlineInputBorder(borderSide: borderSide),
                        hintStyle: TextStyle(color: textColor))),
                if (makeQuote)
                  Container(
                      padding: EdgeInsets.only(top: 12),
                      width: width,
                      child: TextField(
                          controller: _quoteController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                              hintText: appLoc(context).enterYourQuote,
                              fillColor: Colors.transparent,
                              filled: true,
                              border:
                                  OutlineInputBorder(borderSide: borderSide),
                              focusedBorder:
                                  OutlineInputBorder(borderSide: borderSide),
                              enabledBorder:
                                  OutlineInputBorder(borderSide: borderSide),
                              hintStyle: TextStyle(color: textColor))))
              ]);
            })),
        [
          dialogButton(context, cancelButton, DialogButtonType.cancel),
          dialogButton(context, confirmButton, DialogButtonType.primary,
              onTap: () {
            if (makeQuote && _quoteController.text.isEmpty) {
              showSnackBar(context,
                  type: SnackType.info, text: appLoc(context).quoteRequired);
            } else {
              Navigator.pop(context, DialogButtonType.primary);
            }
          })
        ],
        title: title,
        barrierDismissible: false);
    if (res == DialogButtonType.primary &&
        _order != null &&
        _selectedTruck != null) {
      if (mounted) {
        Loader.show(loadingText: appLoc(context).placingOrder);
      }
      final resp = await Functions.shared.takeOrder(
          _order!,
          int.tryParse(_quoteController.text) ?? _order!.price(),
          _selectedTruck!,
          position);
      Loader.hide();
      if (mounted) {
        if (resp != null) {
          setState(() {
            _order = resp;
            updateNegotiation();
          });
          showSnackBar(context,
              type: SnackType.success, text: appLoc(context).operationSuccess);
        } else {
          showSnackBar(context,
              type: SnackType.error, text: appLoc(context).operationFailed);
        }
      }
    }
  }
}
