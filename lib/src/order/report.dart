import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/order.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/selectable.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class ActivityReportView extends StatefulWidget {
  const ActivityReportView({super.key});

  static const String routeName = 'activity_report';

  @override
  State<StatefulWidget> createState() => _ActivityReportViewState();
}

class _ActivityReportViewState extends State<ActivityReportView>
    with PageMixin, EntryMixin, UnfocusMixin, OrderMixin {
  Order? _order;
  ReportItemOption? _selected;
  List<ReportItemOption> _options = [];
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _order = ModalRoute.of(context)!.settings.arguments as Order?;
        _options = ReportItemOption.allReportItemOptions(context);
        _selected = _options.firstOrNull;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return unfocusContainer(pageBase(
        appLoc(context).activityReport,
        Column(children: [
          formItem(
              title: appLoc(context).selectReportItem,
              input: Padding(
                  padding: EdgeInsets.only(top: 16, bottom: 16),
                  child: Column(spacing: 10, children: [
                    ..._options.map((e) => InkWell(
                        onTap: () {
                          setState(() {
                            _selected = e;
                          });
                        },
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              checkBox(
                                  _selected?.identifier() == e.identifier()),
                              Container(
                                  padding: EdgeInsets.only(left: 16),
                                  width:
                                      MediaQuery.of(context).size.width - 160,
                                  child: Text(e.label(),
                                      style: TextStyle(fontSize: 16)))
                            ])))
                  ]))),
          if (_selected != null && _selected!.placeholder.isNotEmpty)
            formItem(
                title: _selected!.text,
                isRequired: true,
                input:
                    textInput(_controller, _selected!.placeholder, maxLines: 3))
        ]),
        operation: BottomOperationPanel.singleOperation(
            context,
            appLoc(context).submit,
            _controller.text.isEmpty &&
                    _selected?.placeholder.isNotEmpty == true
                ? null
                : () => sendActivityReport(_order, _selected!.type,
                    reason: _controller.text))));
  }
}
