import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/order.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:flutter/material.dart';

enum UploadType { weightPhoto, receipt }

class PhotoUploadView extends StatefulWidget {
  final String title;
  final Order order;
  final String subtitle;
  final UploadType type;
  final UpdateOrderFunction function;
  final String functionArgKey;

  const PhotoUploadView(
      {super.key,
      required this.title,
      required this.order,
      required this.subtitle,
      required this.type,
      required this.function,
      required this.functionArgKey});

  static const String routeName = 'photo_upload';

  @override
  State<StatefulWidget> createState() => _PhotoUploadViewState();
}

class _PhotoUploadViewState extends State<PhotoUploadView>
    with PageMixin, EntryMixin, OrderMixin {
  String? _photoStorage;

  @override
  Widget build(BuildContext context) {
    return pageBase(
        widget.title,
        formItem(
            title: widget.subtitle,
            isRequired: true,
            input: imageUpload(_photoStorage, (value) {
              setState(() {
                _photoStorage = value;
              });
            }, widget.type.name, width: 200, allowSelectFromGallery: false)),
        operation: BottomOperationPanel.singleOperation(
            context,
            appLoc(context).submit,
            _photoStorage == null
                ? null
                : () async {
                    Map<String, dynamic> args = {};
                    args[widget.functionArgKey] = _photoStorage;
                    final order = await updateOrder(
                        widget.order, widget.function,
                        args: args);
                    if (order != null && context.mounted) {
                      Navigator.of(context).pop(order);
                    }
                  }));
  }
}
