import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:driver/src/mixins/order.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart';
import 'package:flutter/material.dart';

class OrderLogsView extends StatefulWidget {
  const OrderLogsView({super.key});

  static const String routeName = 'order_logs';

  @override
  State<StatefulWidget> createState() => _OrderLogsViewState();
}

class _OrderLogsViewState extends State<OrderLogsView>
    with PageMixin, OrderMixin {
  List<Map<String, dynamic>> _logs = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final orderId = ModalRoute.of(context)!.settings.arguments as String?;
      if (orderId != null) {
        _logs = await Firestore.shared.getOrderLogs(orderId);
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(
        appLoc(context).logs,
        Column(
            spacing: 10,
            children: _logs
                .map((e) => Container(
                    padding: EdgeInsets.all(10),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4)),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                              (e['createdAt'] as Timestamp)
                                  .toDate()
                                  .toMinuteString(),
                              style: TextStyle(color: Color(0xFF666666))),
                          const SizedBox(height: 8),
                          Text(orderStatusSubtitle(e['status']))
                        ])))
                .toList()));
  }
}
