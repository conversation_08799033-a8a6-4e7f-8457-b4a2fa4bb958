import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';

mixin QuoteMixin<T extends StatefulWidget> on State<T> {
  Widget quotePrice(CityQuote quote) {
    return Row(
      children: [
        Text('\$', style: TextStyle(fontSize: 12)),
        Text('${quote.quotation}', style: TextStyle(fontSize: 16))
      ],
    );
  }

  final double quoteDeltaWidth = 68;
  final double titleWidth = 72;

  Widget title(String title) {
    return Container(
        constraints: BoxConstraints(maxWidth: titleWidth),
        padding: EdgeInsets.only(right: 4),
        child: Text(title, style: TextStyle(color: Color(0xFF999999))));
  }

  Widget quoteDelta(CityQuote quote) {
    return Wrap(children: [
      Container(
          padding: EdgeInsets.symmetric(horizontal: 2, vertical: 2),
          width: quoteDeltaWidth,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: quote.delta > 0
                  ? errorColor
                  : quote.delta == 0
                      ? Colors.grey
                      : primaryColor),
          child: Text(
            '${quote.delta > 0 ? '+' : ''}${(quote.delta * 100).toStringAsFixed(2)}%',
            style: TextStyle(color: Colors.white),
          ))
    ]);
  }

  Widget quoteDeltaNoBackground(CityQuote quote) {
    return Row(children: [
      if (quote.delta != 0)
        Image.asset('assets/images/${quote.delta > 0 ? 'up' : 'down'}.png',
            width: 10),
      Container(
          margin: EdgeInsets.only(left: 4),
          child: Text(
            quote.delta == 0
                ? '--'
                : '${(quote.delta * 100).toStringAsFixed(2)}%',
            style: TextStyle(fontSize: 16),
          ))
    ]);
  }

  Widget quoteCard(CityQuote quote, {void Function()? onTap}) {
    return InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width - 32,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(4)),
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
                padding: EdgeInsets.only(top: 4, right: 12),
                child: Image.asset(
                    truckImage(quote.type,
                        isSelected: true, ignoreSelection: true),
                    width: 80,
                    fit: BoxFit.fitWidth)),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    '${localizedString(SettingsController.shared.origins.firstOrNull?['city'] ?? '')} - ${localizedString(quote.destination)}',
                    style: TextStyle(fontSize: 16)),
                Text(localizedString(quote.typeName),
                    style: TextStyle(color: Color(0xFF666666))),
                const SizedBox(height: 12),
                SizedBox(
                    width: MediaQuery.of(context).size.width - 144,
                    child: Wrap(
                        alignment: WrapAlignment.spaceBetween,
                        runSpacing: 4,
                        children: [
                          FittedBox(
                              child: Row(children: [
                            title(appLoc(context).latestQuote),
                            quotePrice(quote)
                          ])),
                          FittedBox(
                              child: Row(children: [
                            title(appLoc(context).quoteDelta),
                            quoteDelta(quote)
                          ]))
                        ]))
              ],
            )
          ]),
        ));
  }
}
