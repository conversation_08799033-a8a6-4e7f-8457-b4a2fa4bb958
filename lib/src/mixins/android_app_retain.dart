import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

mixin AndroidAppRetainMixin<T extends StatefulWidget> on State<T> {
  final _androidAppRetain = MethodChannel("android_app_retain");

  Widget appRetain(Widget child) {
    return PopScope(
        canPop: Platform.isIOS,
        onPopInvokedWithResult: (bool didPop, T? result) {
          if (didPop) return;
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop(result);
          } else {
            _androidAppRetain.invokeMethod("sendToBackground");
          }
        },
        child: child);
  }
}
