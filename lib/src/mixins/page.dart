import 'package:driver/src/app.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:lsapp/lsapp.dart';

enum EmptyIconType { data, order, truck, bank }

const horizontalPadding = 16.0;
const double topPadding = 8;
const double bottomPadding = 40;
const double buttonHeight = 48;
const double buttonGap = 10;

class CustomFabLocation extends FloatingActionButtonLocation {
  @override
  Offset getOffset(ScaffoldPrelayoutGeometry scaffoldGeometry) {
    // Calculate the desired x and y coordinates here
    // based on scaffoldGeometry properties like:
    // - scaffoldGeometry.scaffoldSize: Size of the entire Scaffold.
    // - scaffoldGeometry.contentBottom: Bottom edge of the content area.
    // - scaffoldGeometry.floatingActionButtonSize: Size of the FAB.
    // - scaffoldGeometry.bottomNavigationBarTop: Top edge of the BottomNavigationBar.

    final double fabWidth = scaffoldGeometry.floatingActionButtonSize.width;
    final double fabHeight = scaffoldGeometry.floatingActionButtonSize.height;
    final double screenWidth = scaffoldGeometry.scaffoldSize.width;
    final double screenHeight = scaffoldGeometry.scaffoldSize.height;

    // Example: Place FAB at a specific offset from the bottom-right
    final double x = screenWidth - fabWidth - 20.0; // 20.0 from right edge
    final double y = screenHeight -
        fabHeight -
        (MediaQuery.of(MyApp.materialKey.currentContext!).padding.bottom +
            bottomPadding +
            buttonHeight +
            48);

    return Offset(x, y);
  }
}

class BottomOperationPanel {
  final Widget panel;
  final double height;

  BottomOperationPanel({required this.panel, required this.height});

  static Widget button(
      BuildContext context, String title, void Function()? onPressed,
      {DialogButtonType buttonType = DialogButtonType.primary,
      double? buttonWidth}) {
    buttonWidth ??= MediaQuery.of(context).size.width;
    return ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
            elevation: 0,
            backgroundColor: buttonType == DialogButtonType.primary
                ? primaryColor
                : Colors.white,
            foregroundColor: buttonType == DialogButtonType.primary
                ? Colors.white
                : primaryColor,
            fixedSize: Size(buttonWidth, buttonHeight),
            alignment: Alignment.center,
            textStyle: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            shape: RoundedRectangleBorder(
                side: buttonType == DialogButtonType.primary
                    ? BorderSide.none
                    : BorderSide(color: primaryColor),
                borderRadius: BorderRadius.circular(8))),
        child: Text(title, textAlign: TextAlign.center));
  }

  static Widget container(BuildContext context, Widget child) {
    return Container(
        color: backgroundColor,
        padding: EdgeInsets.only(
            left: horizontalPadding,
            right: horizontalPadding,
            top: topPadding,
            bottom: MediaQuery.of(context).padding.bottom + bottomPadding),
        width: MediaQuery.of(context).size.width,
        child: child);
  }

  static BottomOperationPanel singleOperation(
      BuildContext context, String title, void Function()? onPressed,
      {DialogButtonType buttonType = DialogButtonType.primary}) {
    return BottomOperationPanel(
        panel: container(
            context, button(context, title, onPressed, buttonType: buttonType)),
        height: topPadding + buttonHeight + bottomPadding);
  }

  static BottomOperationPanel twoRowOperations(BuildContext context,
      {required String title1,
      void Function()? onPressed1,
      required String title2,
      void Function()? onPressed2,
      DialogButtonType buttonType1 = DialogButtonType.primary,
      DialogButtonType buttonType2 = DialogButtonType.secondary}) {
    return BottomOperationPanel(
        panel: container(
            context,
            Column(
              children: [
                button(context, title1, onPressed1, buttonType: buttonType1),
                SizedBox(height: buttonGap),
                button(context, title2, onPressed2, buttonType: buttonType2)
              ],
            )),
        height: topPadding + buttonHeight * 2 + buttonGap + bottomPadding);
  }

  static BottomOperationPanel twoColumnOperations(BuildContext context,
      {required String title1,
      void Function()? onPressed1,
      required String title2,
      void Function()? onPressed2,
      DialogButtonType buttonType1 = DialogButtonType.secondary,
      DialogButtonType buttonType2 = DialogButtonType.primary}) {
    double buttonWidth =
        (MediaQuery.of(context).size.width - buttonGap * 5) / 2;
    return BottomOperationPanel(
        panel: container(
            context,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                button(context, title1, onPressed1,
                    buttonType: buttonType1, buttonWidth: buttonWidth),
                SizedBox(height: buttonGap),
                button(context, title2, onPressed2,
                    buttonType: buttonType2, buttonWidth: buttonWidth)
              ],
            )),
        height: topPadding + buttonHeight + bottomPadding);
  }

  static BottomOperationPanel threeOperations(BuildContext context,
      {required String title1,
      void Function()? onPressed1,
      required String title2,
      void Function()? onPressed2,
      required String title3,
      void Function()? onPressed3,
      DialogButtonType buttonType1 = DialogButtonType.primary,
      DialogButtonType buttonType2 = DialogButtonType.secondary,
      DialogButtonType buttonType3 = DialogButtonType.primary}) {
    double buttonWidth =
        (MediaQuery.of(context).size.width - buttonGap * 5) / 2;
    return BottomOperationPanel(
        panel: container(
            context,
            Column(
              children: [
                button(context, title1, onPressed1, buttonType: buttonType1),
                SizedBox(height: buttonGap),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    button(context, title2, onPressed2,
                        buttonType: buttonType2, buttonWidth: buttonWidth),
                    SizedBox(height: buttonGap),
                    button(context, title3, onPressed3,
                        buttonType: buttonType3, buttonWidth: buttonWidth)
                  ],
                )
              ],
            )),
        height: topPadding + buttonHeight * 2 + buttonGap + bottomPadding);
  }
}

mixin PageMixin<T extends StatefulWidget> on State<T> {
  Widget pageBase(String title, Widget child,
      {bool useScrollView = true,
      PreferredSizeWidget? appBarBottom,
      BottomOperationPanel? operation,
      bool useTransparentAppBar = false,
      Color transparentAppBarForegroundColor = Colors.white,
      List<Widget> actions = const [],
      Widget? backgroundHeader,
      double paddingTop = 12,
      Positioned? fixedContent,
      Widget? floatingActionButton,
      Future<void> Function()? onRefresh}) {
    List<Widget> children = [
      SizedBox(height: MediaQuery.of(context).size.height),
      if (backgroundHeader != null) backgroundHeader
    ];
    Widget scrollView = SingleChildScrollView(
        physics:
            onRefresh != null ? const AlwaysScrollableScrollPhysics() : null,
        padding: EdgeInsets.only(
            left: horizontalPadding,
            right: horizontalPadding,
            top: paddingTop,
            bottom: MediaQuery.of(context).padding.bottom +
                48 +
                (operation?.height ?? 0)),
        child: child);
    if (useScrollView && onRefresh != null) {
      scrollView = RefreshIndicator(
          onRefresh: onRefresh,
          backgroundColor: primaryColor,
          color: Colors.white,
          edgeOffset: 10,
          child: scrollView);
    }
    if (useTransparentAppBar) {
      double appBarHeight = MediaQuery.of(context).padding.top + kToolbarHeight;
      children.addAll([
        AppBar(
          title: Text(title),
          backgroundColor: Colors.transparent,
          foregroundColor: transparentAppBarForegroundColor,
          bottom: appBarBottom,
          actions: actions,
        ),
        Positioned(
            left: 0, right: 0, top: appBarHeight, bottom: 0, child: scrollView)
      ]);
    } else {
      children.add(scrollView);
    }
    children.addAll([
      if (fixedContent != null) fixedContent,
      if (operation != null)
        KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) => isKeyboardVisible
                ? const SizedBox()
                : Positioned(bottom: 0, child: operation.panel))
    ]);
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: !useTransparentAppBar
          ? AppBar(
              title: Text(title),
              backgroundColor: Colors.white,
              bottom: appBarBottom,
              actions: actions,
            )
          : null,
      body: useScrollView ? Stack(children: children) : child,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: CustomFabLocation(),
    );
  }

  Widget emptyIcon(
      {EmptyIconType type = EmptyIconType.data,
      double paddingTop = 100,
      void Function()? onTap}) {
    String image;
    Text text;
    TextStyle textStyle = TextStyle(color: Color(0xFF666666));
    switch (type) {
      case EmptyIconType.data:
        image = 'assets/images/no_data.png';
        text = Text(appLoc(context).noData, style: textStyle);
        break;
      case EmptyIconType.order:
        image = 'assets/images/no_data.png';
        text = Text(appLoc(context).noOrder, style: textStyle);
        break;
      case EmptyIconType.truck:
        image = 'assets/images/no_truck.png';
        text = Text.rich(TextSpan(
          text: appLoc(context).noTruck,
          style: textStyle,
          children: <TextSpan>[
            TextSpan(
                text: appLoc(context).add,
                style: TextStyle(
                    color: primaryColor,
                    decoration: TextDecoration.underline,
                    decorationColor: primaryColor)),
          ],
        ));
        break;
      case EmptyIconType.bank:
        image = 'assets/images/no_bank.png';
        text = Text.rich(TextSpan(
          text: appLoc(context).noBank,
          style: textStyle,
          children: <TextSpan>[
            TextSpan(
                text: appLoc(context).addBank,
                style: TextStyle(
                    color: primaryColor,
                    decoration: TextDecoration.underline,
                    decorationColor: primaryColor)),
          ],
        ));
        break;
    }

    return InkWell(
        onTap: onTap,
        splashFactory: NoSplash.splashFactory,
        highlightColor: Colors.transparent,
        child: Container(
            margin: EdgeInsets.only(top: paddingTop),
            width: MediaQuery.of(context).size.width,
            alignment: Alignment.center,
            child: Column(
              children: [
                Image.asset(image, width: 120),
                const SizedBox(height: 8),
                text,
              ],
            )));
  }

  Widget dropDown<U>(List<DropdownMenuEntry<U>> entries,
      {String? hintText,
      U? initialSelection,
      void Function(U?)? onSelected,
      double width = 240,
      Widget? leadingIcon,
      Widget? trailingIcon,
      TextEditingController? controller,
      TextStyle textStyle =
          const TextStyle(color: Color(0xFF666666), fontSize: 14),
      InputDecorationTheme inputDecorationTheme = const InputDecorationTheme(
          fillColor: Colors.transparent,
          filled: true,
          outlineBorder: BorderSide.none,
          border: InputBorder.none,
          hintStyle: TextStyle(color: Color(0xFF666666))),
      TextAlign textAlign = TextAlign.right}) {
    return DropdownMenu<U>(
      width: width,
      leadingIcon: leadingIcon,
      controller: controller,
      trailingIcon: trailingIcon,
      textStyle: textStyle,
      textAlign: textAlign,
      inputDecorationTheme: inputDecorationTheme,
      initialSelection: initialSelection,
      requestFocusOnTap: false,
      hintText: hintText,
      onSelected: onSelected,
      dropdownMenuEntries: entries,
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.resolveWith((states) {
          return Colors.white;
        }),
      ),
    );
  }

  Widget dismissable(String id, Widget child, String message,
      Future<bool> Function() removeFunc,
      {Future<bool> Function()? canDismiss}) {
    return Dismissible(
        key: Key(id),
        background: Container(
            color: Colors.red,
            child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
              Padding(
                  padding: EdgeInsets.only(right: 24),
                  child: Icon(Icons.delete_forever, color: Colors.white))
            ])),
        confirmDismiss: (_) async {
          if (canDismiss != null && !(await canDismiss())) {
            return false;
          }

          if (mounted) {
            final res =
                await showAppDialog(context, dialogMessage(message: message), [
              dialogButton(context, appLoc(context).confirmDelete,
                  DialogButtonType.cancel),
              dialogButton(context, appLoc(context).cancelDelete,
                  DialogButtonType.primary)
            ]);
            if (res == DialogButtonType.cancel) {
              Loader.show();
              bool removed = await removeFunc();
              Loader.hide();
              return removed;
            }
          }

          return false;
        },
        dismissThresholds: {DismissDirection.endToStart: 0.2},
        direction: DismissDirection.endToStart,
        child: child);
  }

  Widget dataList(Iterable<Widget> children) {
    return Column(spacing: 10, children: [
      ...children,
      Container(
          padding: EdgeInsets.only(top: 12),
          child: Text(appLoc(context).reachBottom,
              style: TextStyle(color: Color(0xFFCCCCCC))))
    ]);
  }
}
