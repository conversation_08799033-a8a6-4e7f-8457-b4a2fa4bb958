import 'dart:io';

import 'package:driver/src/driver/info_entry.dart';
import 'package:driver/src/login/view.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/truck/view.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:driver/src/widgets/webview.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

mixin LoginMixin<T extends StatefulWidget> on State<T> {
  bool agreementChecked = false;

  @override
  void initState() {
    super.initState();
    User.shared.addListener(userChangeListener);
  }

  @override
  void dispose() {
    User.shared.removeListener(userChangeListener);
    super.dispose();
  }

  void userChangeListener() async {
    debugPrint('userChangeListener');
    if (mounted) {
      if (User.shared.hasRegistered()) {
        Loader.hide();
        User.shared.removeListener(userChangeListener);
        Navigator.of(context).pop();
      }
    }
  }

  TextSpan agreementLink(String text, WebResource res,
      {double fontSize = 16,
      FontWeight fontWeight = FontWeight.w500,
      Color color = primaryColor}) {
    final recognizer = TapGestureRecognizer();
    recognizer.onTap = () {
      WebView.open(context, res);
    };
    return TextSpan(
        text: text,
        style:
            TextStyle(color: color, fontSize: fontSize, fontWeight: fontWeight),
        recognizer: recognizer);
  }

  Future<bool> agreeBeforeLogin() async {
    if (agreementChecked) {
      return true;
    }

    return await showAppDialog(
            context,
            dialogMessage(
                messageWidget: RichText(
                    text: TextSpan(style: descStyle, children: [
              TextSpan(text: appLoc(context).notAgreeMessage1),
              agreementLink(
                  appLoc(context).userAgreement, WebResource.agreement),
              agreementLink(
                  appLoc(context).privacyAgreement, WebResource.privacy),
              agreementLink(
                  appLoc(context).infoAgreement, WebResource.information),
              TextSpan(text: appLoc(context).notAgreeMessage2),
            ]))),
            [
              dialogButton(
                  context, appLoc(context).cancel, DialogButtonType.cancel),
              dialogButton(
                  context, appLoc(context).agree, DialogButtonType.primary)
            ],
            title: appLoc(context).readAgreement) ==
        DialogButtonType.primary;
  }

  Widget agreements() {
    TextStyle textStyle = const TextStyle(
        color: Color(0xFF666666),
        fontSize: 16,
        fontWeight: FontWeight.w400,
        height: 1.5);
    return Container(
        padding: const EdgeInsets.all(0),
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          InkWell(
              splashFactory: NoSplash.splashFactory,
              highlightColor: Colors.transparent,
              onTap: () {
                setState(() {
                  agreementChecked = !agreementChecked;
                });
              },
              child: Container(
                  width: 20,
                  height: 20,
                  alignment: Alignment.center,
                  margin: const EdgeInsets.only(right: 8, top: 2),
                  decoration: BoxDecoration(
                      color: agreementChecked ? primaryColor : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                          color: agreementChecked
                              ? primaryColor
                              : const Color(0xFF999999))),
                  child: Icon(Icons.check,
                      size: 12,
                      color: agreementChecked
                          ? Colors.white
                          : Colors.transparent))),
          SizedBox(
              width: MediaQuery.of(context).size.width - 76,
              child: RichText(
                  text: TextSpan(style: textStyle, children: [
                TextSpan(
                    text: appLoc(context).agreementText,
                    style: textStyle,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        setState(() {
                          agreementChecked = !agreementChecked;
                        });
                      }),
                agreementLink(
                    appLoc(context).userAgreement, WebResource.agreement),
                agreementLink(
                    appLoc(context).privacyAgreement, WebResource.privacy),
                agreementLink(
                    appLoc(context).infoAgreement, WebResource.information),
                TextSpan(
                    text: appLoc(context).agreementText1, style: textStyle),
              ])))
        ]));
  }

  Widget platformLoginButton() {
    if (Platform.isAndroid) {
      return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: OutlinedButton(
              onPressed: () async {
                if (await agreeBeforeLogin() && mounted) {
                  setState(() {
                    agreementChecked = true;
                  });

                  Loader.show();
                  await User.shared.logout();
                  if (mounted) {
                    final ret =
                        await authAction(context, () => signInWithGoogle());
                    debugPrint('signInWithGoogle $ret');
                    if (ret == null) {
                      // auth failed
                      Loader.hide();
                    }
                  } else {
                    Loader.hide();
                  }
                }
              },
              child:
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Image.asset(
                  'assets/images/google.png',
                  width: 24,
                ),
                const SizedBox(width: 8, height: 44),
                Text(appLoc(context).googleLogin,
                    style: const TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: Colors.black))
              ])));
    }

    if (Platform.isIOS) {
      return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: OutlinedButton(
              onPressed: () async {
                if (await agreeBeforeLogin() && mounted) {
                  setState(() {
                    agreementChecked = true;
                  });

                  Loader.show();
                  await User.shared.logout();
                  if (mounted) {
                    final ret =
                        await authAction(context, () => signInWithApple());
                    if (ret == null) {
                      // auth failed
                      if (mounted) {
                        showSnackBar(context,
                            text: 'Auth failed, please try again later.',
                            type: SnackType.error);
                      }
                      Loader.hide();
                    }
                  } else {
                    Loader.hide();
                  }
                }
              },
              child:
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Image.asset(
                  'assets/images/apple.png',
                  width: 24,
                ),
                const SizedBox(width: 8, height: 44),
                Text(appLoc(context).appleLogin,
                    style: const TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: Colors.black))
              ])));
    }

    return const SizedBox();
  }

  static void startUserLogin(BuildContext context) {
    if (!User.shared.hasRegistered()) {
      Navigator.of(context).pushNamed(LoginView.routeName);
    } else if (User.shared.driverVerification == null) {
      Navigator.of(context).pushNamed(DriverInfoEntryView.routeName);
    } else if (User.shared.truckVerifications.isEmpty) {
      Navigator.of(context).pushNamed(TruckManagementView.routeName);
    }
  }
}
