import 'package:driver/src/mixins/login.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  static const String routeName = 'login';

  @override
  State<StatefulWidget> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> with LoginMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Image.asset('assets/images/login_background.jpg',
            fit: BoxFit.cover,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width),
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: MediaQuery.of(context).padding.top),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  icon: Icon(Icons.close, size: 28))
                            ]),
                        clickableIcon(),
                        const SizedBox(height: 10),
                        Text(appLoc(context).welcome,
                            style: TextStyle(
                                fontSize: 28, fontWeight: FontWeight.w500)),
                        Text(appLoc(context).appTitle,
                            style: TextStyle(
                                fontSize: 28, fontWeight: FontWeight.w500)),
                      ]),
                  Column(children: [
                    platformLoginButton(),
                    if (_guestAccessShown)
                      Padding(
                          padding: EdgeInsets.only(bottom: 12),
                          child: TextButton(
                              onPressed: guestAccess,
                              child: Text(appLoc(context).guestAccess,
                                  style: TextStyle(color: primaryColor)))),
                    agreements()
                  ]),
                  Padding(
                      padding: EdgeInsets.only(bottom: 40),
                      child: TextButton(
                          onPressed: customerService,
                          child: Text(appLoc(context).contactUs,
                              style: TextStyle(
                                  color: Color(0xFF666666), fontSize: 16))))
                ]))
      ],
    ));
  }

  int lastTap = 0;
  int consecutiveTaps = 0;
  bool _guestAccessShown = false;

  Widget clickableIcon() {
    return settingProvider((settings) {
      return GestureDetector(
          onTap: settings.allowGuestAccess &&
                  settings.guestAccessCred['email'] != null
              ? () {
                  int now = DateTime.now().millisecondsSinceEpoch;
                  if (now - lastTap < 1000) {
                    consecutiveTaps++;
                    if (consecutiveTaps >= 3) {
                      consecutiveTaps = 0;
                      if (!_guestAccessShown) {
                        _guestAccessShown = true;
                        setState(() {});
                      }
                    }
                  } else {
                    consecutiveTaps = 0;
                  }
                  lastTap = now;
                }
              : null,
          child: Container(
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
              clipBehavior: Clip.antiAlias,
              child: Image.asset('assets/images/logo.png',
                  height: 48, width: 48)));
    });
  }

  Future guestAccess() async {
    if (await agreeBeforeLogin() && mounted) {
      setState(() {
        agreementChecked = true;
      });

      Loader.show();
      await User.shared.logout();
      Map<String, dynamic> cred = SettingsController.shared.guestAccessCred;
      String? email = cred['email'] as String?;
      String? pwd = cred['password'] as String?;
      if (mounted && email != null && pwd != null) {
        final ret = await authAction(context, () => signInUser(email, pwd));
        debugPrint('signInUser $ret');
        if (ret == null) {
          // auth failed
          Loader.hide();
        }
      } else {
        Loader.hide();
      }
    }
  }
}
