import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/utils/firestore.dart' as fs;

class BankInfoEntryView extends StatefulWidget {
  const BankInfoEntryView({super.key});

  static const String routeName = 'bank_info_entry';

  @override
  State<StatefulWidget> createState() => _BankInfoEntryViewState();
}

class _BankInfoEntryViewState extends State<BankInfoEntryView>
    with UnfocusMixin, EntryMixin, PageMixin {
  BankType _type = BankType.company;
  final TextEditingController _companyName = TextEditingController();
  final TextEditingController _companyBank = TextEditingController();
  final TextEditingController _companyAccount = TextEditingController();
  final TextEditingController _companySwift = TextEditingController();
  final TextEditingController _companyAddress = TextEditingController();

  final TextEditingController _businessBank = TextEditingController();
  final TextEditingController _businessBic = TextEditingController();
  final TextEditingController _businessBin = TextEditingController();
  final TextEditingController _businessKbe = TextEditingController();
  final TextEditingController _businessKnp = TextEditingController();
  final TextEditingController _businessAccount = TextEditingController();
  final TextEditingController _businessOwnerName = TextEditingController();
  final TextEditingController _businessAddress = TextEditingController();

  @override
  Widget build(BuildContext context) {
    List<Widget> form;
    if (_type == BankType.company) {
      form = companyForm();
    } else {
      form = businessForm();
    }
    return unfocusContainer(pageBase(
        appLoc(context).addBankTitle,
        Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [typeSelect(), ...form]),
        operation: BottomOperationPanel.singleOperation(
            context, appLoc(context).submit, canSubmit() ? submit : null)));
  }

  Widget typeSelect() {
    double selectTextWidth = MediaQuery.of(context).size.width * 0.35;
    return Padding(
        padding: EdgeInsets.only(bottom: 24),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(appLoc(context).selectBankType,
              style: TextStyle(color: Color(0xFF666666))),
          const SizedBox(height: 10),
          Row(children: [
            InkWell(
                splashFactory: NoSplash.splashFactory,
                highlightColor: Colors.transparent,
                onTap: () {
                  setState(() {
                    _type = BankType.company;
                  });
                },
                child: Row(children: [
                  checkBox(_type == BankType.company),
                  Container(
                      padding: EdgeInsets.only(left: 8),
                      width: selectTextWidth,
                      child: Text(appLoc(context).company,
                          style: TextStyle(fontSize: 16)))
                ])),
            const SizedBox(width: 24),
            InkWell(
                splashFactory: NoSplash.splashFactory,
                highlightColor: Colors.transparent,
                onTap: () {
                  setState(() {
                    _type = BankType.business;
                  });
                },
                child: Row(children: [
                  checkBox(_type == BankType.business),
                  Container(
                      padding: EdgeInsets.only(left: 8),
                      width: selectTextWidth,
                      child: Text(appLoc(context).business,
                          style: TextStyle(fontSize: 16)))
                ]))
          ])
        ]));
  }

  List<Widget> companyForm() {
    return [
      formItem(
          title: appLoc(context).bankName,
          isRequired: true,
          input: textInput(_companyBank, appLoc(context).bankNamePlaceholder,
              maxLength: 64)),
      formItem(
          title: appLoc(context).swiftAccount,
          isRequired: true,
          input: textInput(
              _companySwift, appLoc(context).swiftAccountPlaceholder,
              maxLength: 32, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).companyName,
          isRequired: true,
          input: textInput(_companyName, appLoc(context).companyNamePlaceholder,
              maxLength: 64)),
      formItem(
          title: appLoc(context).bankAccount,
          isRequired: true,
          input: textInput(
              _companyAccount, appLoc(context).bankAccountPlaceholder,
              maxLength: 32, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).registeredAddress,
          isRequired: true,
          input: textInput(
              _companyAddress, appLoc(context).registeredAddressPlaceholder,
              maxLength: 128))
    ];
  }

  List<Widget> businessForm() {
    return [
      formItem(
          title: appLoc(context).bankName,
          isRequired: true,
          input: textInput(_businessBank, appLoc(context).bankNamePlaceholder,
              maxLength: 64)),
      formItem(
          title: appLoc(context).bic,
          isRequired: true,
          input: textInput(_businessBic, appLoc(context).bicPlaceholder,
              maxLength: 32, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).bin,
          isRequired: true,
          input: textInput(_businessBin, appLoc(context).binPlaceholder,
              maxLength: 64, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).kbe,
          isRequired: true,
          input: textInput(_businessKbe, appLoc(context).kbePlaceholder,
              maxLength: 32, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).knp,
          isRequired: true,
          input: textInput(_businessKnp, appLoc(context).knpPlaceholder,
              maxLength: 32, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).bankAccount,
          isRequired: true,
          input: textInput(
              _businessAccount, appLoc(context).bankAccountPlaceholder,
              maxLength: 32, onlyAlphanumeric: true)),
      formItem(
          title: appLoc(context).businessOwnerName,
          isRequired: true,
          input: textInput(
              _businessOwnerName, appLoc(context).businessOwnerNamePlaceholder,
              maxLength: 64)),
      formItem(
          title: appLoc(context).registeredAddress,
          isRequired: true,
          input: textInput(
              _businessAddress, appLoc(context).registeredAddressPlaceholder,
              maxLength: 128))
    ];
  }

  bool canSubmit() {
    if (_type == BankType.company) {
      return _companyAddress.text.isNotEmpty &&
          _companyBank.text.isNotEmpty &&
          _companyAccount.text.isNotEmpty &&
          _companyName.text.isNotEmpty &&
          _companySwift.text.isNotEmpty;
    } else {
      return _businessBank.text.isNotEmpty &&
          _businessAccount.text.isNotEmpty &&
          _businessOwnerName.text.isNotEmpty &&
          _businessAddress.text.isNotEmpty &&
          _businessBic.text.isNotEmpty &&
          _businessBin.text.isNotEmpty &&
          _businessKbe.text.isNotEmpty &&
          _businessKnp.text.isNotEmpty;
    }
  }

  Future submit() async {
    Loader.show();
    final res = await fs.Firestore.shared.createBankInfo(
        _type == BankType.company
            ? CompanyBankInfo(
                name: _companyName.text,
                account: _companyAccount.text,
                bank: _companyBank.text,
                swift: _companySwift.text,
                address: _companyAddress.text)
            : BusinessBankInfo(
                name: _businessOwnerName.text,
                account: _businessAccount.text,
                bank: _businessBank.text,
                bic: _businessBic.text,
                bin: _businessBin.text,
                kbe: _businessKbe.text,
                knp: _businessKnp.text,
                address: _businessAddress.text));
    Loader.hide();
    if (res != null && mounted) {
      showSnackBar(context,
          type: SnackType.success, text: appLoc(context).submitSuccess);
      Navigator.of(context).pop(res);
    }
  }
}
