import 'package:driver/src/utils/const.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

final TextStyle titleStyle =
    TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w500);
final TextStyle descStyle = TextStyle(
    color: Color(0xFF353535),
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.5);
const TextStyle buttonStyle = TextStyle(
    color: Color(0xFFFFFFFF), fontSize: 16, fontWeight: FontWeight.w500);
const TextStyle cancelButtonStyle = TextStyle(
    color: Color(0xFF000000), fontSize: 16, fontWeight: FontWeight.w500);

Future<DialogButtonType?> showAppDialog(
    BuildContext context, Widget message, List<Widget> buttons,
    {String? title,
    Widget? titleWidget,
    bool barrierDismissible = true,
    bool showCancel = false,
    double titleTopPadding = 32}) async {
  if (title != null) {
    titleWidget = Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      Container(
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.width * 0.7,
          child: Text(title, style: titleStyle, textAlign: TextAlign.center))
    ]);
  }
  return await showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => CustomDialog(
          padding: 0,
          bottomPadding: 0,
          bgDecoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: const Color(0xFFFFFFFF)),
          showCancel: showCancel,
          crossAxisAlignment: CrossAxisAlignment.center,
          titleWidget: Padding(
              padding: EdgeInsets.only(top: titleTopPadding),
              child: titleWidget),
          widgetContent: Column(
            children: [
              message,
              Container(
                  decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.vertical(bottom: Radius.circular(10))),
                  clipBehavior: Clip.antiAlias,
                  child: IntrinsicHeight(
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: buttons)))
            ],
          )));
}

Widget dialogMessage({String? message, Widget? messageWidget}) {
  return Container(
    padding: const EdgeInsets.only(top: 16, bottom: 30, left: 24, right: 24),
    alignment: Alignment.center,
    width: double.infinity,
    child: message != null ? Text(message, style: descStyle) : messageWidget,
  );
}

Widget dialogButton(BuildContext context, String title, DialogButtonType type,
    {void Function()? onTap}) {
  return Expanded(
      child: InkWell(
          onTap: onTap ??
              () {
                Navigator.of(context).pop(type);
              },
          child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                  color: type == DialogButtonType.cancel
                      ? Color(0xFFF1F1F1)
                      : primaryColor),
              child: Text(title,
                  textAlign: TextAlign.center,
                  style: type == DialogButtonType.cancel
                      ? cancelButtonStyle
                      : buttonStyle))));
}
