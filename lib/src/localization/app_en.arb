{"appTitle": "GoFreight", "welcome": "Welcome to", "use": "Welcome to use", "googleLogin": "Google Login", "appleLogin": "Apple Login", "agreementText": "I have read and agree to", "userAgreement": " <GoFreight Software User Agreement> ", "privacyAgreement": "<GoFreight Software Privacy Agreement> and ", "infoAgreement": "<GoFreight Information Commitment Agreement>", "agreementText1": ".", "contactUs": "Contact us if you encounter problems", "notAgreeMessage1": "We detected that you have not read the agreement in detail. Please read carefully and agree to", "notAgreeMessage2": ". Please click 'Agree' to start using our products and services.", "cancel": "Cancel", "agree": "Agree", "readAgreement": "Read Agreement", "home": "Home", "order": "Orders", "mine": "Profile", "networkError": "Network error, please try again later", "loadFrom": "Load from {origin}", "@loadFrom": {"placeholders": {"origin": {}}}, "selectDestinationCountry": "Please select your destination country", "loadingTruck": "Transport Vehicle: ", "loadingTruckNotRegistered": "You have not registered vehicle information yet, go ahead and ", "registerTruck": "Complete Vehicle Information.", "loadingTruckNotVerified": "Vehicle information not verified, please ", "quoteCenter": "Quote Center", "quoteFrom": "Departing from {origin}", "@quoteFrom": {"placeholders": {"origin": {}}}, "rankByPrice": "Sort by High Price", "rankByPref": "Sort by Preference", "pickupDate": "Pickup Date:", "distance": "Total {distance}km", "@distance": {"placeholders": {"distance": {}}}, "loading": "Loading", "unloading": "Unloading", "noOrder": "No orders", "noData": "No data", "viewOrder": "View Order", "rankByPrefNotice": "You need to first set up loading/unloading locations and transport vehicles above before you can set preferences", "login": "<PERSON><PERSON>", "verifyTitle": "Verify as platform driver for quick order acceptance.", "verifyAction": "Start Verification", "massiveOrders": "Massive Orders", "fastPay": "Timely Settlement", "safe": "Platform Guarantee", "driverCenter": "Driver Center", "driverVerify": "Driver Verification", "truckManagement": "Vehicle Management", "bankInfo": "Bank Information", "serviceCenter": "Service Center", "customerService": "Contact Customer Service", "guarantee": "Information Commitment Agreement", "userAgreementAction": "User Agreement", "privacyAgreementAction": "Privacy Agreement", "switchLanguage": "Switch Language", "completeInfo": "Please complete information", "userInfo": "Personal Information", "phone": "Phone", "phonePlaceholder": "Please enter your phone number", "driverName": "Driver Name (English)", "driverNamePlaceholder": "Please enter your name", "passportPhoto": "Passport Photo", "moreContactInfo": "More Contact Information", "chinesePhone": "China Phone", "chinesePhonePlaceholder": "Please enter your China phone number", "wechat": "WeChat ID", "wechatPlaceholder": "Please enter your WeChat ID", "save": "Save", "quickEntry": "Quick Entry", "quickEntryMessage": "Based on your input, we found the following matching information. You can select the correct information for quick entry", "quickEntryNotAvailable": "Please continue to enter the following information", "confirmSelect": "Confirm", "cancelSelect": "None of these", "imageFromAlbum": "Select from Album", "imageFromCamera": "Take Photo", "allQuoteTruckTypes": "All Vehicle Types", "allQuoteAreas": "All Areas", "latestQuote": "Latest Quote", "quoteDelta": "Daily Change", "quoteDetail": "Quote Det<PERSON>", "quoteHistory": "Quote History", "quoteTime": "Quote Time", "quotePrice": "Price", "waitForDriverVerificationTitle": "Submit for Review", "waitForDriverVerificationMessage": "You have successfully submitted for review. We will complete the review within 3 business days. Please keep your phone available. Would you like to proceed with vehicle verification?", "waitForTruckVerificationMessage": "You have successfully submitted for review. We will complete the review within 3 business days. Please keep your phone available. You can start accepting orders immediately after approval.", "notYet": "Not yet", "goVerify": "Verify Now", "understand": "Got it", "noTruck": "No vehicles yet, go ahead and ", "add": "Add", "addTruckTitle": "Add Vehicle", "license": "License Plate", "licensePlaceholder": "Please enter license plate number", "licenseImage": "Vehicle Registration Certificate", "next": "Next", "truckType": "Vehicle Type", "tonnage": "Vehicle Tonnage", "tonnagePlaceholder": "Please select tonnage", "volume": "Vehicle Volume", "volumePlaceholder": "Please select volume", "confirm": "Confirm", "verified": "Verified", "verificationEmpty": "Pending", "verificationPending": "Verifying", "verificationRejected": "Failed", "verificationRejectedReason": "Please contact customer service", "deleteTruckMessage": "You are deleting the associated vehicle. Confirm deletion?", "confirmDelete": "Confirm Delete", "cancelDelete": "Don't Delete", "noBank": "No bank information yet, go ahead and ", "addBank": "Add", "addBankTitle": "Add Bank Information", "selectBankType": "Select Account Type", "company": "Company", "business": "Individual Business", "bankName": "Bank Name", "bankNamePlaceholder": "Please enter bank name", "bankAccount": "Account Number", "bankAccountPlaceholder": "Please enter account number", "swiftAccount": "SWIFT Code", "swiftAccountPlaceholder": "Please enter SWIFT code", "businessOwnerName": "Name", "businessOwnerNamePlaceholder": "Please enter individual business owner name", "companyName": "Account Name", "companyNamePlaceholder": "Please enter account name", "registeredAddress": "Registration Address", "registeredAddressPlaceholder": "Please enter registration address", "bic": "BIC", "bicPlaceholder": "Please enter BIC", "bin": "BIN/INN", "binPlaceholder": "Please enter BIN/INN", "kbe": "Kbe", "kbePlaceholder": "Please enter Kbe", "knp": "Knp", "knpPlaceholder": "Please enter Knp", "submit": "Submit", "deleteBankMessage": "You are deleting bank information. Confirm deletion?", "submitSuccess": "Submit successful", "switchTruck": " Switch", "loadingImage": "Loading image", "selectTruck": "Select Vehicle", "uid": "User ID", "copyToClipboard": "Copied to clipboard", "preventTruckDeleteTitle": "Vehicle information under verification", "preventTruckDelete": "Vehicle information is under verification. Please try again later or contact customer service for expedited processing.", "contactForVerification": "Contact customer service for expedited verification", "notVerifiedDriverTitle": "Not verified", "notVerifiedDriverMessage": "Your information is incomplete and you cannot accept orders currently. Please complete your information promptly to start accepting orders", "notVerifiedTruckTitle": "No vehicles", "notVerifiedTruckMessage": "You have not registered vehicle information yet and cannot accept orders currently. Please complete vehicle information promptly to start accepting orders", "verifyingDriverTitle": "Under verification", "verifyingDriverMessage": "Your information is under verification and you cannot accept orders currently. You can contact customer service for expedited verification", "verifyingTruckTitle": "Vehicle under verification", "verifyingTruckMessage": "Your vehicle information is under verification and you cannot accept orders currently. You can contact customer service for expedited verification", "orderDetail": "Order Details", "orderDistance": "Order Total Distance", "orderLoadingAddress": "Loading Location", "orderUnloadingAddress": "Unloading Location", "orderLoadingTime": "Loading Date", "orderCost": "Freight Cost", "makeQuote": "Quote Negotiation", "placeOrder": "Accept Order Now", "orderManagement": "Order Management", "orderStatusMatching": "Matching", "orderStatusOngoing": "Ongoing", "orderStatusFinished": "Completed", "orderStatusCanceled": "Cancelled", "allCities": "All Areas", "allPrices": "All Prices", "priceAbove": "and above", "customerServiceWithWhatsapp": "You are about to contact customer service via WhatsApp.", "customerServiceWithPhone": "You are about to contact customer service via phone.", "contact": "Contact", "orderTargetPrice": "Current shipper quote", "confirmQuote": "Confirm Quote", "cancelQuote": "<PERSON><PERSON> Quote", "selectYourTruck": "Please select your vehicle", "enterYourQuote": "Please enter your quote", "operationSuccess": "Operation successful", "operationFailed": "Operation failed, please try again later", "quoteRequired": "Please enter your quote", "confirmPlaceOrder": "Confirm Order", "cancelPlaceOrder": "Cancel Order", "placingOrder": "Processing order, please wait", "notifications": "Notifications", "statusFindingSubtitle": "Shipper published new order", "statusMatchingSubtitle": "System is filtering and matching", "statusPendingConfirmSubtitle": "Interested in order, waiting for shipper confirmation", "statusPendingContractSubtitle": "Contract is being signed, please pay attention to phone calls", "statusPendingLoadSubtitle": "Order accepted successfully, please go to loading location as soon as possible", "statusQueueingSubtitle": "In queue, please wait patiently", "statusTransportSubtitle": "Transportation started, remember to report position daily", "statusInClearanceSubtitle": "In customs clearance queue, please wait patiently", "statusHeadingDestinationSubtitle": "Heading to destination, please report any issues during transport", "statusPendingUnloadSubtitle": "Arrived at destination, please unload promptly", "statusCompleteSubtitle": "Order completed, thank you for your safe delivery", "statusCancelledSubtitle": "Order cancelled, please contact us if you have any questions", "contactService": "Contact Service", "orderInvalidStatus": "Invalid", "logs": "Logs", "weightPhoto": "Weight Photo", "weightPhotoPlaceholder": "Please upload vehicle weight photo", "finishLoad": "Loading Complete", "activityReport": "Activity Report", "locationNotAvailableTitle": "Location Failed", "locationNotAvailableMessage": "Location access is required to process transport orders. Please check if your location permission is enabled.", "openSettings": "Open Settings", "acquiringLocation": "Acquiring location", "viewPayment": "View Receipt", "temporaryStay": "Temporary Rest", "temporaryStayPlaceholder": "Please enter rest reason", "unexpectedDelay": "Unexpected Delay", "unexpectedDelayPlaceholder": "Please enter delay reason", "selectReportItem": "Please select report item", "acquireLocationFailed": "Location failed, please try again later", "dailyReport": "Daily Report", "startClearance": "Start Clearance", "endClearance": "Clearance Complete", "arriveDestination": "Arrive at Destination", "unloadComplete": "Unloading Complete", "uploadReceipt": "Upload Delivery Receipt", "uploadReceiptPlaceholder": "Please upload delivery receipt photo", "cannotTakeOrderTitle": "Cannot Accept Order", "cannotTakeOrderMessage": "You currently have an ongoing order. Please complete the current order before accepting new orders.", "signOut": "Sign Out", "signOutMessage": "You are signing out of your account. Please confirm if you want to sign out.", "deleteAccount": "Delete Account", "deleteAccountMessage": "You are deleting your account. Data will be cleared after deletion. Please confirm.", "accountDeletedMessage": "Account deleted. Please contact customer service.", "unauthenticatedError": "App authentication failed. Please download the app from official channels", "onGoingOrder": "Ongoing Order", "orderWeightPhotoMessage": "Please go to the loading location as soon as possible to load cargo. Remember to take weight photos.", "orderFinishLoadMessage": "Have you finished loading? If loading is complete, please click 'Loading Complete'.", "orderStartClearanceMessage": "You may have arrived at customs clearance. Please confirm if clearance has started.", "orderEndClearanceMessage": "You may have completed customs clearance. Please confirm if clearance is complete.", "orderArriveDestinationMessage": "You may have arrived at the unloading location. Please confirm if you have reached the destination.", "orderUploadReceiptMessage": "You may have completed unloading. Please upload the delivery receipt.", "orderPositionReportMessage": "You have an ongoing order. Please click to report position.", "reportPosition": "Report Position", "guestAccess": "Guest Access", "reachBottom": "- Reached the bottom -", "notVerifiedCannotViewQuotes": "Your information is incomplete and you cannot view quote details currently. Please complete your information promptly.", "orderId": "Order ID"}