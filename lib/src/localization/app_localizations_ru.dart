// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'GoFreight';

  @override
  String get welcome => 'Добро пожаловать в';

  @override
  String get use => 'Добро пожаловать';

  @override
  String get googleLogin => 'Вход с помощью Google';

  @override
  String get appleLogin => 'Авторизация Apple';

  @override
  String get agreementText => 'Я прочитал и согласен с ';

  @override
  String get userAgreement =>
      '《Соглашением об использовании программного обеспечения GoFreight》、';

  @override
  String get privacyAgreement =>
      '《Соглашением о конфиденциальности GoFreight》、';

  @override
  String get infoAgreement =>
      '《Соглашением о предоставлении информации GoFreight》';

  @override
  String get agreementText1 => '.';

  @override
  String get contactUs => 'Если возникли проблемы, свяжитесь с нами';

  @override
  String get notAgreeMessage1 =>
      'Обнаружено, что вы еще не прочитали соглашение. Пожалуйста, внимательно прочитайте и примите';

  @override
  String get notAgreeMessage2 =>
      ', Нажмите «Согласен», чтобы начать пользоваться нашими продуктами и услугами.';

  @override
  String get cancel => 'Отменить';

  @override
  String get agree => 'Согласен';

  @override
  String get readAgreement => 'Прочитайте соглашение';

  @override
  String get home => 'Главная';

  @override
  String get order => 'Заказ';

  @override
  String get mine => 'Мой';

  @override
  String get networkError => 'Сетевая аномалия，Пожалуйста попробуйте позже';

  @override
  String loadFrom(Object origin) {
    return 'Из $origin';
  }

  @override
  String get selectDestinationCountry =>
      'Выберите страну, в которую вы собираетесь отправиться';

  @override
  String get loadingTruck => 'Транспортное средство: ';

  @override
  String get loadingTruckNotRegistered =>
      'Вы не сохранили информацию о транспортном средстве，Пойти';

  @override
  String get registerTruck =>
      'Усовершенствование информации о транспортном средстве';

  @override
  String get loadingTruckNotVerified =>
      'Информация об автомобиле не сертифицирована，Пожалуйста ';

  @override
  String get quoteCenter => 'Центр цен';

  @override
  String quoteFrom(Object origin) {
    return 'Отправление из $origin';
  }

  @override
  String get rankByPrice => 'Сортировка по цене';

  @override
  String get rankByPref => 'Сортировка по предпочтениям';

  @override
  String get pickupDate => 'Время получения:';

  @override
  String distance(Object distance) {
    return 'Общая протяженность ${distance}km';
  }

  @override
  String get loading => 'Погрузка';

  @override
  String get unloading => 'Разгрузка';

  @override
  String get noOrder => 'Нет заказов';

  @override
  String get noData => 'Нет данных';

  @override
  String get viewOrder => 'Просмотреть заказ';

  @override
  String get rankByPrefNotice =>
      'Вам необходимо сейчас вверху указать место погрузки и разгрузки, а также транспортное средство，только после этого вы сможете установить предпочтения';

  @override
  String get login => 'Вход';

  @override
  String get verifyTitle =>
      'Сертифицированный водитель платформы, быстрое принятие заказа';

  @override
  String get verifyAction => 'Перейти к сертификации';

  @override
  String get massiveOrders => 'Большое количество заказов';

  @override
  String get fastPay => 'Своевременные расчеты';

  @override
  String get safe => 'Гарантии платформы';

  @override
  String get driverCenter => 'Центр водителей';

  @override
  String get driverVerify => 'Сертификация водителей';

  @override
  String get truckManagement => 'Управление транспортными средствами';

  @override
  String get bankInfo => 'Банковская информация';

  @override
  String get serviceCenter => 'Сервисный центр';

  @override
  String get customerService => 'Обратитесь в службу поддержки клиентов';

  @override
  String get guarantee =>
      'Соглашение о обязательстве предоставления информации';

  @override
  String get userAgreementAction => 'Пользовательское соглашение';

  @override
  String get privacyAgreementAction => 'Политика конфиденциальности';

  @override
  String get switchLanguage => 'Смена языка';

  @override
  String get completeInfo => 'Пожалуйста, дополните информацию';

  @override
  String get userInfo => 'Личная информация';

  @override
  String get phone => 'Телефон ';

  @override
  String get phonePlaceholder => 'Пожалуйста введите номер телефона';

  @override
  String get driverName => 'Имя водителя（На английском языке）';

  @override
  String get driverNamePlaceholder => 'Введите свое имя';

  @override
  String get passportPhoto => 'Фотография';

  @override
  String get moreContactInfo => 'Больше контактов';

  @override
  String get chinesePhone => 'Телефон в Китае';

  @override
  String get chinesePhonePlaceholder => 'Введите свой телефон в Китае';

  @override
  String get wechat => 'WeChat';

  @override
  String get wechatPlaceholder => 'Введите свой WeChat ID';

  @override
  String get save => 'Сохранить';

  @override
  String get quickEntry => 'Быстрый ввод';

  @override
  String get quickEntryMessage =>
      'На основании данных, которые вы заполнили，были найдены следующие совпадающие сведения，Вы можете выбрать правильную информацию для быстрого заполнения';

  @override
  String get quickEntryNotAvailable =>
      'Продолжайте вводить следующую информацию';

  @override
  String get confirmSelect => 'подтвердить';

  @override
  String get cancelSelect => 'отменить';

  @override
  String get imageFromAlbum => 'Выберите из альбомов';

  @override
  String get imageFromCamera => 'Фотографировать ';

  @override
  String get allQuoteTruckTypes => 'Все модели';

  @override
  String get allQuoteAreas => 'Все регионы';

  @override
  String get latestQuote => 'Последние предложения';

  @override
  String get quoteDelta => 'Ежедневный рост/падение';

  @override
  String get quoteDetail => 'Детальная информация';

  @override
  String get quoteHistory => 'Исторические котировки';

  @override
  String get quoteTime => 'Время котировки';

  @override
  String get quotePrice => 'Цена';

  @override
  String get waitForDriverVerificationTitle => 'Отправить на проверку';

  @override
  String get waitForDriverVerificationMessage =>
      'Вы успешно подали заявку на аудит， мы будем проверены в течение 3 рабочих дней，в течение этого периода пожалуйста держите телефон гладким。 ли транспортное средство сертификации？';

  @override
  String get waitForTruckVerificationMessage =>
      'Вы были успешно отправлены на аудит，мы будем проверены в течение 3 рабочих дней，в течение этого периода пожалуйста держите телефон гладким，проверено немедленное получение заказов。';

  @override
  String get notYet => 'Сейчас не могу';

  @override
  String get goVerify => 'Пройти проверку';

  @override
  String get understand => 'Понятно';

  @override
  String get noTruck => 'Нет транспортных средств, добавить ';

  @override
  String get add => 'новый';

  @override
  String get addTruckTitle => 'новый добавленный автомобиль';

  @override
  String get license => 'номерной знак';

  @override
  String get licensePlaceholder => 'Пожалуйста введите номер машины';

  @override
  String get licenseImage => 'Технический паспорт';

  @override
  String get next => 'Следующий';

  @override
  String get truckType => 'Тип автомобиля';

  @override
  String get tonnage => 'Грузоподъемность';

  @override
  String get tonnagePlaceholder => 'Пожалуйста выберите тоннаж';

  @override
  String get volume => 'Объем кузова';

  @override
  String get volumePlaceholder => 'Пожалуйста выберите объем';

  @override
  String get confirm => 'Подтверждено';

  @override
  String get verified => 'Сертифицированный';

  @override
  String get verificationEmpty => 'в ожидании';

  @override
  String get verificationPending => 'Проверка';

  @override
  String get verificationRejected => 'Неуспешный';

  @override
  String get verificationRejectedReason =>
      'Обратитесь в службу поддержки клиентов';

  @override
  String get deleteTruckMessage =>
      'Вы удаляете связанные транспортные средства. Подтвердить удаление?';

  @override
  String get confirmDelete => 'удалить';

  @override
  String get cancelDelete => 'Не удалять';

  @override
  String get noBank => 'Информация о банке отсутствует, ';

  @override
  String get addBank => 'добавить';

  @override
  String get addBankTitle => 'Добавлена информация о банке';

  @override
  String get selectBankType => 'Выберите тип счета';

  @override
  String get company => 'Счет компании';

  @override
  String get business => 'Индивидуальный предприниматель';

  @override
  String get bankName => 'Название банка, открывающего счет';

  @override
  String get bankNamePlaceholder => 'Пожалуйста, введите название банка';

  @override
  String get bankAccount => 'счет';

  @override
  String get bankAccountPlaceholder => 'Пожалуйста, введите номер счета';

  @override
  String get swiftAccount => 'SWIFT коды';

  @override
  String get swiftAccountPlaceholder => 'Пожалуйста, введите код SWIFT';

  @override
  String get businessOwnerName => 'Имя';

  @override
  String get businessOwnerNamePlaceholder =>
      'Пожалуйста, введите имя индивидуального владельца бизнеса';

  @override
  String get companyName => 'Имя учетной записи';

  @override
  String get companyNamePlaceholder => 'Пожалуйста, введите Имя учетной записи';

  @override
  String get registeredAddress => 'Юридический адрес';

  @override
  String get registeredAddressPlaceholder =>
      'Пожалуйста, введите ваш регистрационный адрес';

  @override
  String get bic => 'BIC';

  @override
  String get bicPlaceholder => 'Пожалуйста, введите BIC';

  @override
  String get bin => 'BIN/INN';

  @override
  String get binPlaceholder => 'Пожалуйста, введите BIN/INN';

  @override
  String get kbe => 'Kbe';

  @override
  String get kbePlaceholder => 'Пожалуйста, введите Kbe';

  @override
  String get knp => 'Knp';

  @override
  String get knpPlaceholder => 'Пожалуйста, введите Knp';

  @override
  String get submit => 'представить';

  @override
  String get deleteBankMessage =>
      'Вы удаляете свои банковские реквизиты，подтверждаете удаление？';

  @override
  String get submitSuccess => 'Отправлено';

  @override
  String get switchTruck => '\nс переключением';

  @override
  String get loadingImage => 'Сейчас загружается изображение';

  @override
  String get selectTruck => 'Выбор транспортных средств';

  @override
  String get uid => 'Номер пользователя';

  @override
  String get copyToClipboard => 'Скопировано в буфер обмена';

  @override
  String get preventTruckDeleteTitle =>
      'Выполняется проверка подлинности информации о транспортном средстве';

  @override
  String get preventTruckDelete =>
      'Информация о транспортном средстве проходит проверку подлинности，пожалуйста подождите некоторое время или обратитесь в службу поддержки，чтобы ускорить процесс。';

  @override
  String get contactForVerification =>
      'Обратитесь в службу поддержки чтобы ускорить сертификацию';

  @override
  String get notVerifiedDriverTitle => 'Не подтверждено';

  @override
  String get notVerifiedDriverMessage =>
      'Ваши данные неполны, в настоящее время мы не можем принимать заказы. Пожалуйста дополните свои данные, чтобы сразу начать принимать заказы';

  @override
  String get notVerifiedTruckTitle =>
      'В настоящее время нет транспортных средств';

  @override
  String get notVerifiedTruckMessage =>
      'Вы еще не обновили информацию о транспортном средстве, поэтому в настоящее время не можете принимать заказы. Пожалуйста, дополните информацию о транспортном средстве и сразу же начните принимать заказы';

  @override
  String get verifyingDriverTitle => 'Выполняется аутентификация';

  @override
  String get verifyingDriverMessage =>
      'Ваша информация проходит сертификацию， в настоящее время мы не можем принимать заказы，вы можете связаться со службой поддержки чтобы ускорить сертификацию';

  @override
  String get verifyingTruckTitle => 'Проводится сертификация автомобилей';

  @override
  String get verifyingTruckMessage =>
      'Ваша информация проходит сертификацию，в настоящее время мы не можем принимать заказы，вы можете связаться со службой поддержки чтобы ускорить сертификацию';

  @override
  String get orderDetail => 'Детали заказа';

  @override
  String get orderDistance => 'Общая дистанция заказа';

  @override
  String get orderLoadingAddress => 'Место погрузки';

  @override
  String get orderUnloadingAddress => 'Место разгрузки';

  @override
  String get orderLoadingTime => 'Дата погрузки';

  @override
  String get orderCost => 'Плата за перевозку';

  @override
  String get makeQuote => 'Переговоров о цене';

  @override
  String get placeOrder => 'Принять заказ немедленно';

  @override
  String get orderManagement => 'Управление заказами';

  @override
  String get orderStatusMatching => 'совпадение';

  @override
  String get orderStatusOngoing => 'продолжить';

  @override
  String get orderStatusFinished => 'завершено';

  @override
  String get orderStatusCanceled => 'отмена';

  @override
  String get allCities => 'Все регионы';

  @override
  String get allPrices => 'Все цены';

  @override
  String get priceAbove => ' Более';

  @override
  String get customerServiceWithWhatsapp =>
      'Скоро свяжусь с службой поддержки через WhatsAp';

  @override
  String get customerServiceWithPhone =>
      'Скоро свяжусь с службой поддержки по телефону';

  @override
  String get contact => 'Связаться';

  @override
  String get orderTargetPrice => 'Текущие цены от грузоотправителей';

  @override
  String get confirmQuote => 'Потвердить цену';

  @override
  String get cancelQuote => 'Отменить цену';

  @override
  String get selectYourTruck => 'Пожалуйста выберите ваш автомобиль';

  @override
  String get enterYourQuote => 'Пожалуйста введите свою цену';

  @override
  String get operationSuccess => 'Операция прошла успешно';

  @override
  String get operationFailed =>
      'Операция не удалась，Пожалуйста повторите попытку позднее';

  @override
  String get quoteRequired => 'Пожалуйста введите свою цену';

  @override
  String get confirmPlaceOrder => 'Подтвердить прием заказа';

  @override
  String get cancelPlaceOrder => 'Отменить прием заказа';

  @override
  String get placingOrder => 'аказ обрабатывается, пожалуйста подождите';

  @override
  String get notifications => 'Уведомления';

  @override
  String get statusFindingSubtitle =>
      'Грузоотправитель опубликовал новый заказ';

  @override
  String get statusMatchingSubtitle => 'Система в процессе отбора и подбора';

  @override
  String get statusPendingConfirmSubtitle =>
      'Интенция принять заказ уже есть，ожидается подтверждение грузоотправителя';

  @override
  String get statusPendingContractSubtitle =>
      'Договор находится в процессе подписания，следите за звонками';

  @override
  String get statusPendingLoadSubtitle =>
      'Прием заказа успешен，пожалуйста как можно раньше направитесь на место погрузки для погрузки';

  @override
  String get statusQueueingSubtitle =>
      'В очереди，пожалуйста подождите с терпением';

  @override
  String get statusTransportSubtitle =>
      'Транспортировка началась，Не забудьте ежедневно сообщать о своей локации';

  @override
  String get statusInClearanceSubtitle =>
      'В очереди на таможенную очистку， пожалуйста подождите с терпением';

  @override
  String get statusHeadingDestinationSubtitle =>
      'Направляюсь к пункту назначения，При возникновении проблем во время транспортировки сообщайте вовремя';

  @override
  String get statusPendingUnloadSubtitle =>
      'Уже достигнут пункт назначения，пожалуйста своевременно выполните разгрузку';

  @override
  String get statusCompleteSubtitle =>
      'Заказ завершен， Спасибо за вашу сопровождение в пути';

  @override
  String get statusCancelledSubtitle =>
      'Заказ отменен，При возникновении вопросов пожалуйста свяжитесь для обсуждения';

  @override
  String get contactService => 'Связаться и обсудить';

  @override
  String get orderInvalidStatus => 'Устарело';

  @override
  String get logs => 'Журнал';

  @override
  String get weightPhoto => 'Сфотографировать и взвесить';

  @override
  String get weightPhotoPlaceholder =>
      'Пожалуйста загрузите фотографию взвешивания транспортного средства';

  @override
  String get finishLoad => 'Погрузка завершена';

  @override
  String get activityReport => 'Сообщение о событии';

  @override
  String get locationNotAvailableTitle =>
      'Определение местоположения не удалось';

  @override
  String get locationNotAvailableMessage =>
      'Для обработки транспортного заказа необходимо получить ваше местоположение，Пожалуйста проверьте включены ли разрешения на определение местоположения。';

  @override
  String get openSettings => 'Открыть настройки';

  @override
  String get acquiringLocation => 'Получение местоположения в процессе';

  @override
  String get viewPayment => 'Просмотреть документы основания';

  @override
  String get temporaryStay => 'Временный отдых';

  @override
  String get temporaryStayPlaceholder => 'Пожалуйста введите причину отдыха';

  @override
  String get unexpectedDelay => 'Особые задержки';

  @override
  String get unexpectedDelayPlaceholder =>
      'Пожалуйста введите причину задержки';

  @override
  String get selectReportItem => 'Выберите тип события';

  @override
  String get acquireLocationFailed =>
      'Определение местоположения не удалось，Пожалуйста попробуйте позже';

  @override
  String get dailyReport => 'Ежедневная регистрация';

  @override
  String get startClearance => 'Начать таможенную очистку';

  @override
  String get endClearance => 'Таможенная очистка завершена';

  @override
  String get arriveDestination => 'Прибыло на место назначения';

  @override
  String get unloadComplete => 'Разгрузка завершена';

  @override
  String get uploadReceipt => 'Загрузить акт приемки передачи';

  @override
  String get uploadReceiptPlaceholder =>
      'Пожалуйста загрузите фотографию акта приемки передачи';

  @override
  String get cannotTakeOrderTitle => 'Нельзя принять заказ';

  @override
  String get cannotTakeOrderMessage =>
      'У вас в настоящее время есть активный заказ，Пожалуйста завершите текущий заказ прежде чем принимать новый。';

  @override
  String get signOut => 'Выйти из системы';

  @override
  String get signOutMessage =>
      'Вы выходите из учетной записи，Пожалуйста подтвердите хотите ли вы выйти。';

  @override
  String get deleteAccount => 'Удалить учетную запись';

  @override
  String get deleteAccountMessage =>
      'Вы удаляете свою учетную запись，После удаления данные будут очищены，Пожалуйста подтвердите。';

  @override
  String get accountDeletedMessage =>
      'Учетная запись была удалена，Пожалуйста свяжитесь с поддержкой。';

  @override
  String get unauthenticatedError =>
      'Аутентификация приложения не удалась，Пожалуйста скачайте приложение через официальные каналы';

  @override
  String get onGoingOrder => 'Активный заказ';

  @override
  String get orderWeightPhotoMessage =>
      'Пожалуйста как можно скорее направьтесь на место погрузки для погрузки，Не забудьте обязательно взвесить и сфотографировать。';

  @override
  String get orderFinishLoadMessage =>
      'Вы уже завершили погрузку，Если да пожалуйста нажмите，Погрузка завершена。';

  @override
  String get orderStartClearanceMessage =>
      'Вероятно вы прибыли к таможне для таможенной очистки，Пожалуйста подтвердите началась ли таможенная очистка。';

  @override
  String get orderEndClearanceMessage =>
      'Вероятно вы завершили таможенную очистку，Пожалуйста подтвердите  завершена таможенная очистка。';

  @override
  String get orderArriveDestinationMessage =>
      'Вероятно вы уже прибыли до места разгрузки，Пожалуйста подтвердите прибыли до места назначения。';

  @override
  String get orderUploadReceiptMessage =>
      'Вероятно вы уже завершили разгрузку，Пожалуйста загрузите акт приемки передачи。';

  @override
  String get orderPositionReportMessage =>
      'У вас есть активный заказ，Пожалуйста нажмите чтобы сообщить о местоположении。';

  @override
  String get reportPosition => 'Сообщить о местоположении';

  @override
  String get guestAccess => 'Guest Access';

  @override
  String get reachBottom => '- Уже дошли до конца -';

  @override
  String get notVerifiedCannotViewQuotes =>
      'Ваши данные неполные， В настоящее время вы не можете просматривать детали рыночной ситуации，Пожалуйста своевременно дополните ваши данные。';

  @override
  String get orderId => 'идентификатор заказа';
}
