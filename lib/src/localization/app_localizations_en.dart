// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'GoFreight';

  @override
  String get welcome => 'Welcome to';

  @override
  String get use => 'Welcome to use';

  @override
  String get googleLogin => 'Google Login';

  @override
  String get appleLogin => 'Apple Login';

  @override
  String get agreementText => 'I have read and agree to';

  @override
  String get userAgreement => ' <GoFreight Software User Agreement> ';

  @override
  String get privacyAgreement => '<GoFreight Software Privacy Agreement> and ';

  @override
  String get infoAgreement => '<GoFreight Information Commitment Agreement>';

  @override
  String get agreementText1 => '.';

  @override
  String get contactUs => 'Contact us if you encounter problems';

  @override
  String get notAgreeMessage1 =>
      'We detected that you have not read the agreement in detail. Please read carefully and agree to';

  @override
  String get notAgreeMessage2 =>
      '. Please click \'Agree\' to start using our products and services.';

  @override
  String get cancel => 'Cancel';

  @override
  String get agree => 'Agree';

  @override
  String get readAgreement => 'Read Agreement';

  @override
  String get home => 'Home';

  @override
  String get order => 'Orders';

  @override
  String get mine => 'Profile';

  @override
  String get networkError => 'Network error, please try again later';

  @override
  String loadFrom(Object origin) {
    return 'Load from $origin';
  }

  @override
  String get selectDestinationCountry =>
      'Please select your destination country';

  @override
  String get loadingTruck => 'Transport Vehicle: ';

  @override
  String get loadingTruckNotRegistered =>
      'You have not registered vehicle information yet, go ahead and ';

  @override
  String get registerTruck => 'Complete Vehicle Information.';

  @override
  String get loadingTruckNotVerified =>
      'Vehicle information not verified, please ';

  @override
  String get quoteCenter => 'Quote Center';

  @override
  String quoteFrom(Object origin) {
    return 'Departing from $origin';
  }

  @override
  String get rankByPrice => 'Sort by High Price';

  @override
  String get rankByPref => 'Sort by Preference';

  @override
  String get pickupDate => 'Pickup Date:';

  @override
  String distance(Object distance) {
    return 'Total ${distance}km';
  }

  @override
  String get loading => 'Loading';

  @override
  String get unloading => 'Unloading';

  @override
  String get noOrder => 'No orders';

  @override
  String get noData => 'No data';

  @override
  String get viewOrder => 'View Order';

  @override
  String get rankByPrefNotice =>
      'You need to first set up loading/unloading locations and transport vehicles above before you can set preferences';

  @override
  String get login => 'Login';

  @override
  String get verifyTitle =>
      'Verify as platform driver for quick order acceptance.';

  @override
  String get verifyAction => 'Start Verification';

  @override
  String get massiveOrders => 'Massive Orders';

  @override
  String get fastPay => 'Timely Settlement';

  @override
  String get safe => 'Platform Guarantee';

  @override
  String get driverCenter => 'Driver Center';

  @override
  String get driverVerify => 'Driver Verification';

  @override
  String get truckManagement => 'Vehicle Management';

  @override
  String get bankInfo => 'Bank Information';

  @override
  String get serviceCenter => 'Service Center';

  @override
  String get customerService => 'Contact Customer Service';

  @override
  String get guarantee => 'Information Commitment Agreement';

  @override
  String get userAgreementAction => 'User Agreement';

  @override
  String get privacyAgreementAction => 'Privacy Agreement';

  @override
  String get switchLanguage => 'Switch Language';

  @override
  String get completeInfo => 'Please complete information';

  @override
  String get userInfo => 'Personal Information';

  @override
  String get phone => 'Phone';

  @override
  String get phonePlaceholder => 'Please enter your phone number';

  @override
  String get driverName => 'Driver Name (English)';

  @override
  String get driverNamePlaceholder => 'Please enter your name';

  @override
  String get passportPhoto => 'Passport Photo';

  @override
  String get moreContactInfo => 'More Contact Information';

  @override
  String get chinesePhone => 'China Phone';

  @override
  String get chinesePhonePlaceholder => 'Please enter your China phone number';

  @override
  String get wechat => 'WeChat ID';

  @override
  String get wechatPlaceholder => 'Please enter your WeChat ID';

  @override
  String get save => 'Save';

  @override
  String get quickEntry => 'Quick Entry';

  @override
  String get quickEntryMessage =>
      'Based on your input, we found the following matching information. You can select the correct information for quick entry';

  @override
  String get quickEntryNotAvailable =>
      'Please continue to enter the following information';

  @override
  String get confirmSelect => 'Confirm';

  @override
  String get cancelSelect => 'None of these';

  @override
  String get imageFromAlbum => 'Select from Album';

  @override
  String get imageFromCamera => 'Take Photo';

  @override
  String get allQuoteTruckTypes => 'All Vehicle Types';

  @override
  String get allQuoteAreas => 'All Areas';

  @override
  String get latestQuote => 'Latest Quote';

  @override
  String get quoteDelta => 'Daily Change';

  @override
  String get quoteDetail => 'Quote Details';

  @override
  String get quoteHistory => 'Quote History';

  @override
  String get quoteTime => 'Quote Time';

  @override
  String get quotePrice => 'Price';

  @override
  String get waitForDriverVerificationTitle => 'Submit for Review';

  @override
  String get waitForDriverVerificationMessage =>
      'You have successfully submitted for review. We will complete the review within 3 business days. Please keep your phone available. Would you like to proceed with vehicle verification?';

  @override
  String get waitForTruckVerificationMessage =>
      'You have successfully submitted for review. We will complete the review within 3 business days. Please keep your phone available. You can start accepting orders immediately after approval.';

  @override
  String get notYet => 'Not yet';

  @override
  String get goVerify => 'Verify Now';

  @override
  String get understand => 'Got it';

  @override
  String get noTruck => 'No vehicles yet, go ahead and ';

  @override
  String get add => 'Add';

  @override
  String get addTruckTitle => 'Add Vehicle';

  @override
  String get license => 'License Plate';

  @override
  String get licensePlaceholder => 'Please enter license plate number';

  @override
  String get licenseImage => 'Vehicle Registration Certificate';

  @override
  String get next => 'Next';

  @override
  String get truckType => 'Vehicle Type';

  @override
  String get tonnage => 'Vehicle Tonnage';

  @override
  String get tonnagePlaceholder => 'Please select tonnage';

  @override
  String get volume => 'Vehicle Volume';

  @override
  String get volumePlaceholder => 'Please select volume';

  @override
  String get confirm => 'Confirm';

  @override
  String get verified => 'Verified';

  @override
  String get verificationEmpty => 'Pending';

  @override
  String get verificationPending => 'Verifying';

  @override
  String get verificationRejected => 'Failed';

  @override
  String get verificationRejectedReason => 'Please contact customer service';

  @override
  String get deleteTruckMessage =>
      'You are deleting the associated vehicle. Confirm deletion?';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get cancelDelete => 'Don\'t Delete';

  @override
  String get noBank => 'No bank information yet, go ahead and ';

  @override
  String get addBank => 'Add';

  @override
  String get addBankTitle => 'Add Bank Information';

  @override
  String get selectBankType => 'Select Account Type';

  @override
  String get company => 'Company';

  @override
  String get business => 'Individual Business';

  @override
  String get bankName => 'Bank Name';

  @override
  String get bankNamePlaceholder => 'Please enter bank name';

  @override
  String get bankAccount => 'Account Number';

  @override
  String get bankAccountPlaceholder => 'Please enter account number';

  @override
  String get swiftAccount => 'SWIFT Code';

  @override
  String get swiftAccountPlaceholder => 'Please enter SWIFT code';

  @override
  String get businessOwnerName => 'Name';

  @override
  String get businessOwnerNamePlaceholder =>
      'Please enter individual business owner name';

  @override
  String get companyName => 'Account Name';

  @override
  String get companyNamePlaceholder => 'Please enter account name';

  @override
  String get registeredAddress => 'Registration Address';

  @override
  String get registeredAddressPlaceholder =>
      'Please enter registration address';

  @override
  String get bic => 'BIC';

  @override
  String get bicPlaceholder => 'Please enter BIC';

  @override
  String get bin => 'BIN/INN';

  @override
  String get binPlaceholder => 'Please enter BIN/INN';

  @override
  String get kbe => 'Kbe';

  @override
  String get kbePlaceholder => 'Please enter Kbe';

  @override
  String get knp => 'Knp';

  @override
  String get knpPlaceholder => 'Please enter Knp';

  @override
  String get submit => 'Submit';

  @override
  String get deleteBankMessage =>
      'You are deleting bank information. Confirm deletion?';

  @override
  String get submitSuccess => 'Submit successful';

  @override
  String get switchTruck => ' Switch';

  @override
  String get loadingImage => 'Loading image';

  @override
  String get selectTruck => 'Select Vehicle';

  @override
  String get uid => 'User ID';

  @override
  String get copyToClipboard => 'Copied to clipboard';

  @override
  String get preventTruckDeleteTitle =>
      'Vehicle information under verification';

  @override
  String get preventTruckDelete =>
      'Vehicle information is under verification. Please try again later or contact customer service for expedited processing.';

  @override
  String get contactForVerification =>
      'Contact customer service for expedited verification';

  @override
  String get notVerifiedDriverTitle => 'Not verified';

  @override
  String get notVerifiedDriverMessage =>
      'Your information is incomplete and you cannot accept orders currently. Please complete your information promptly to start accepting orders';

  @override
  String get notVerifiedTruckTitle => 'No vehicles';

  @override
  String get notVerifiedTruckMessage =>
      'You have not registered vehicle information yet and cannot accept orders currently. Please complete vehicle information promptly to start accepting orders';

  @override
  String get verifyingDriverTitle => 'Under verification';

  @override
  String get verifyingDriverMessage =>
      'Your information is under verification and you cannot accept orders currently. You can contact customer service for expedited verification';

  @override
  String get verifyingTruckTitle => 'Vehicle under verification';

  @override
  String get verifyingTruckMessage =>
      'Your vehicle information is under verification and you cannot accept orders currently. You can contact customer service for expedited verification';

  @override
  String get orderDetail => 'Order Details';

  @override
  String get orderDistance => 'Order Total Distance';

  @override
  String get orderLoadingAddress => 'Loading Location';

  @override
  String get orderUnloadingAddress => 'Unloading Location';

  @override
  String get orderLoadingTime => 'Loading Date';

  @override
  String get orderCost => 'Freight Cost';

  @override
  String get makeQuote => 'Quote Negotiation';

  @override
  String get placeOrder => 'Accept Order Now';

  @override
  String get orderManagement => 'Order Management';

  @override
  String get orderStatusMatching => 'Matching';

  @override
  String get orderStatusOngoing => 'Ongoing';

  @override
  String get orderStatusFinished => 'Completed';

  @override
  String get orderStatusCanceled => 'Cancelled';

  @override
  String get allCities => 'All Areas';

  @override
  String get allPrices => 'All Prices';

  @override
  String get priceAbove => 'and above';

  @override
  String get customerServiceWithWhatsapp =>
      'You are about to contact customer service via WhatsApp.';

  @override
  String get customerServiceWithPhone =>
      'You are about to contact customer service via phone.';

  @override
  String get contact => 'Contact';

  @override
  String get orderTargetPrice => 'Current shipper quote';

  @override
  String get confirmQuote => 'Confirm Quote';

  @override
  String get cancelQuote => 'Cancel Quote';

  @override
  String get selectYourTruck => 'Please select your vehicle';

  @override
  String get enterYourQuote => 'Please enter your quote';

  @override
  String get operationSuccess => 'Operation successful';

  @override
  String get operationFailed => 'Operation failed, please try again later';

  @override
  String get quoteRequired => 'Please enter your quote';

  @override
  String get confirmPlaceOrder => 'Confirm Order';

  @override
  String get cancelPlaceOrder => 'Cancel Order';

  @override
  String get placingOrder => 'Processing order, please wait';

  @override
  String get notifications => 'Notifications';

  @override
  String get statusFindingSubtitle => 'Shipper published new order';

  @override
  String get statusMatchingSubtitle => 'System is filtering and matching';

  @override
  String get statusPendingConfirmSubtitle =>
      'Interested in order, waiting for shipper confirmation';

  @override
  String get statusPendingContractSubtitle =>
      'Contract is being signed, please pay attention to phone calls';

  @override
  String get statusPendingLoadSubtitle =>
      'Order accepted successfully, please go to loading location as soon as possible';

  @override
  String get statusQueueingSubtitle => 'In queue, please wait patiently';

  @override
  String get statusTransportSubtitle =>
      'Transportation started, remember to report position daily';

  @override
  String get statusInClearanceSubtitle =>
      'In customs clearance queue, please wait patiently';

  @override
  String get statusHeadingDestinationSubtitle =>
      'Heading to destination, please report any issues during transport';

  @override
  String get statusPendingUnloadSubtitle =>
      'Arrived at destination, please unload promptly';

  @override
  String get statusCompleteSubtitle =>
      'Order completed, thank you for your safe delivery';

  @override
  String get statusCancelledSubtitle =>
      'Order cancelled, please contact us if you have any questions';

  @override
  String get contactService => 'Contact Service';

  @override
  String get orderInvalidStatus => 'Invalid';

  @override
  String get logs => 'Logs';

  @override
  String get weightPhoto => 'Weight Photo';

  @override
  String get weightPhotoPlaceholder => 'Please upload vehicle weight photo';

  @override
  String get finishLoad => 'Loading Complete';

  @override
  String get activityReport => 'Activity Report';

  @override
  String get locationNotAvailableTitle => 'Location Failed';

  @override
  String get locationNotAvailableMessage =>
      'Location access is required to process transport orders. Please check if your location permission is enabled.';

  @override
  String get openSettings => 'Open Settings';

  @override
  String get acquiringLocation => 'Acquiring location';

  @override
  String get viewPayment => 'View Receipt';

  @override
  String get temporaryStay => 'Temporary Rest';

  @override
  String get temporaryStayPlaceholder => 'Please enter rest reason';

  @override
  String get unexpectedDelay => 'Unexpected Delay';

  @override
  String get unexpectedDelayPlaceholder => 'Please enter delay reason';

  @override
  String get selectReportItem => 'Please select report item';

  @override
  String get acquireLocationFailed => 'Location failed, please try again later';

  @override
  String get dailyReport => 'Daily Report';

  @override
  String get startClearance => 'Start Clearance';

  @override
  String get endClearance => 'Clearance Complete';

  @override
  String get arriveDestination => 'Arrive at Destination';

  @override
  String get unloadComplete => 'Unloading Complete';

  @override
  String get uploadReceipt => 'Upload Delivery Receipt';

  @override
  String get uploadReceiptPlaceholder => 'Please upload delivery receipt photo';

  @override
  String get cannotTakeOrderTitle => 'Cannot Accept Order';

  @override
  String get cannotTakeOrderMessage =>
      'You currently have an ongoing order. Please complete the current order before accepting new orders.';

  @override
  String get signOut => 'Sign Out';

  @override
  String get signOutMessage =>
      'You are signing out of your account. Please confirm if you want to sign out.';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get deleteAccountMessage =>
      'You are deleting your account. Data will be cleared after deletion. Please confirm.';

  @override
  String get accountDeletedMessage =>
      'Account deleted. Please contact customer service.';

  @override
  String get unauthenticatedError =>
      'App authentication failed. Please download the app from official channels';

  @override
  String get onGoingOrder => 'Ongoing Order';

  @override
  String get orderWeightPhotoMessage =>
      'Please go to the loading location as soon as possible to load cargo. Remember to take weight photos.';

  @override
  String get orderFinishLoadMessage =>
      'Have you finished loading? If loading is complete, please click \'Loading Complete\'.';

  @override
  String get orderStartClearanceMessage =>
      'You may have arrived at customs clearance. Please confirm if clearance has started.';

  @override
  String get orderEndClearanceMessage =>
      'You may have completed customs clearance. Please confirm if clearance is complete.';

  @override
  String get orderArriveDestinationMessage =>
      'You may have arrived at the unloading location. Please confirm if you have reached the destination.';

  @override
  String get orderUploadReceiptMessage =>
      'You may have completed unloading. Please upload the delivery receipt.';

  @override
  String get orderPositionReportMessage =>
      'You have an ongoing order. Please click to report position.';

  @override
  String get reportPosition => 'Report Position';

  @override
  String get guestAccess => 'Guest Access';

  @override
  String get reachBottom => '- Reached the bottom -';

  @override
  String get notVerifiedCannotViewQuotes =>
      'Your information is incomplete and you cannot view quote details currently. Please complete your information promptly.';

  @override
  String get orderId => 'Order ID';
}
