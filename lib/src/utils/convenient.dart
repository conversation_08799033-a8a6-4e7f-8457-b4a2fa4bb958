import 'package:driver/src/app.dart';
import 'package:driver/src/localization/app_localizations.dart'
    show AppLocalizations;
import 'package:driver/src/model/user.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';
import 'package:url_launcher/url_launcher.dart';

AppLocalizations appLoc(BuildContext context) {
  if (context.mounted) {
    return AppLocalizations.of(context)!;
  }

  return AppLocalizations.of(MyApp.materialKey.currentContext!)!;
}

String localizedString(String str) {
  return SettingsController.shared.translate(str);
}

String maskPrice(User user, int price) {
  String str = '$price';
  if (user.driverVerification != null) return str;
  if (str.isEmpty) {
    return str;
  }
  if (str.length < 3) {
    return '${str[0]}*';
  }
  return str.substring(0, str.length ~/ 3) +
      '*' * (str.length - 2 * (str.length ~/ 3)) +
      str.substring(str.length - str.length ~/ 3);
}

String maskString(String str, {int length = 10}) {
  if (str.isEmpty) {
    return str;
  }
  if (str.length < length) {
    return '${str[0]}*';
  }
  return str.substring(0, length ~/ 3) +
      '*' * (str.length - 2 * (str.length ~/ 3)) +
      str.substring(str.length - str.length ~/ 3);
}

Future customerService() async {
  String phone = SettingsController.shared.customerService['phone'] ??
      defaultCustomerServiceNumber;
  String whatsappUrl =
      'whatsapp://send?phone=${SettingsController.shared.customerService['whatsapp'] ?? defaultCustomerServiceNumber}';
  bool hasWhatsapp = await canLaunchUrl(Uri.parse(whatsappUrl));
  BuildContext context = MyApp.materialKey.currentContext!;
  if (context.mounted) {
    final res = await showAppDialog(
        context,
        dialogMessage(
            message: hasWhatsapp
                ? appLoc(context).customerServiceWithWhatsapp
                : appLoc(context).customerServiceWithPhone),
        [
          dialogButton(
              context, appLoc(context).cancel, DialogButtonType.cancel),
          dialogButton(
              context, appLoc(context).contact, DialogButtonType.primary)
        ]);
    if (res == DialogButtonType.primary) {
      if (hasWhatsapp) {
        await launchUrl(Uri.parse(whatsappUrl));
      } else {
        await launchUrl(Uri.parse('tel:$phone'));
      }
    }
  }
}

extension DateTimeFormat on DateTime {
  String toDateString() {
    return toIso8601String().split('T')[0];
  }

  String toMinuteString() {
    List<String> split = toIso8601String().split('T');
    return '${split[0]} ${split[1].substring(0, 5)}';
  }
}

void handleError(e) {
  if (e is FirebaseException && e.code == 'unauthenticated') {
    showSnackBar(MyApp.materialKey.currentContext!,
        type: SnackType.error,
        text: appLoc(MyApp.materialKey.currentContext!).unauthenticatedError);
  } else {
    debugPrint('$e');
  }
}
