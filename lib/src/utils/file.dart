import 'package:driver/src/model/user.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lsapp/lsapp.dart';

Future<String?> pickImageAndUploadToStorage(BuildContext context, String type,
    {bool allowSelectFromGallery = true}) async {
  final ret = await showModalBottomSheet(
      context: context,
      builder: (ctx) {
        return Container(
            padding: EdgeInsets.symmetric(vertical: 10),
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).padding.bottom + 120,
            child: Column(children: [
              TextButton(
                  onPressed: () =>
                      Navigator.of(context).pop(ImageSource.camera),
                  child: Text(appLoc(context).imageFromCamera,
                      style: TextStyle(color: Colors.black, fontSize: 16))),
              if (allowSelectFromGallery)
                TextButton(
                    onPressed: () =>
                        Navigator.of(context).pop(ImageSource.gallery),
                    child: Text(appLoc(context).imageFromAlbum,
                        style: TextStyle(color: Colors.black, fontSize: 16)))
            ]));
      });
  if (ret != null) {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
        source: ret, maxHeight: 1600, maxWidth: 1600, imageQuality: 80);
    if (image != null) {
      Loader.show();
      try {
        final storage = FirebaseStorage.instance.ref();
        final ref =
            storage.child('$type/${User.shared.user!.uid}/${image.name}');
        await ref.putData(await image.readAsBytes());
        return ref.fullPath;
      } catch (e) {
        handleError(e);
      } finally {
        Loader.hide();
      }
    }
  }

  return null;
}
