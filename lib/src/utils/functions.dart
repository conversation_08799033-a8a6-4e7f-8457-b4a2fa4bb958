import 'package:cloud_functions/cloud_functions.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:geolocator/geolocator.dart';

enum UpdateOrderFunction {
  startLoading,
  endLoading,
  startClearance,
  endClearance,
  startUnloading,
  endUnloading
}

class Functions {
  static Functions shared = Functions();

  Future<List<Driver>> findDriversWithPhoneNumber(String phone) async {
    try {
      final result = await FirebaseFunctions.instance
          .httpsCallable('findDriversWithPhoneNumber')
          .call(
        {"phone": phone},
      );
      if (result.data is List && result.data.isNotEmpty) {
        return List<Driver>.from(result.data
            .map((e) => Driver.fromMap(Map<String, dynamic>.from(e))));
      }
    } catch (e) {
      handleError(e);
    }
    return [];
  }

  Future<List<Truck>> findTrucksWithLicense(String license) async {
    try {
      final result = await FirebaseFunctions.instance
          .httpsCallable('findTruckWithLicense')
          .call(
        {"license": license},
      );
      if (result.data is List && result.data.isNotEmpty) {
        return List<Truck>.from(result.data
            .map((e) => Truck.fromMap(Map<String, dynamic>.from(e))));
      }
    } catch (e) {
      handleError(e);
    }
    return [];
  }

  Future<String?> getStorageImage(
      String id, String token, String recordId, String funcName) async {
    if (token.isEmpty) return null;
    try {
      final result =
          await FirebaseFunctions.instance.httpsCallable(funcName).call(
        {"id": id, "token": token, "recordId": recordId},
      );
      return result.data['path'] as String?;
    } catch (e) {
      handleError(e);
    }

    return null;
  }

  Future<bool> deleteTruckVerification(TruckVerification truck) async {
    if (User.shared.user?.uid == truck.uid && truck.recordId != null) {
      try {
        final result = await FirebaseFunctions.instance
            .httpsCallable('deleteFirestoreDocAndSyncLark')
            .call(
          {
            "db": 'truckVerification',
            "id": truck.id,
            "recordId": truck.recordId
          },
        );
        return (result.data as Map?)?['code'] == 0;
      } catch (e) {
        handleError(e);
      }
    }

    return false;
  }

  Future<bool> deleteBankInfo(BankInfo bank) async {
    if (User.shared.user?.uid == bank.uid && bank.recordId != null) {
      try {
        final result = await FirebaseFunctions.instance
            .httpsCallable('deleteFirestoreDocAndSyncLark')
            .call(
          {"db": 'bankInfo', "id": bank.id, "recordId": bank.recordId},
        );
        return (result.data as Map?)?['code'] == 0;
      } catch (e) {
        handleError(e);
      }
    }

    return false;
  }

  Future<Order?> takeOrder(Order order, int quotation, TruckVerification truck,
      Position position) async {
    if (User.shared.canViewOrder()) {
      try {
        final result =
            await FirebaseFunctions.instance.httpsCallable('takeOrder').call(
          {
            'orderId': order.orderId,
            'orderRecordId': order.recordId,
            'targetPrice': order.targetPrice,
            'platformPrice': order.platformPrice,
            'quotation': quotation,
            'truckId': truck.truckId,
            'driverId': User.shared.driverVerification?.driverId,
            'position': position.toJson()
          },
        );

        final map = result.data['order'];
        if (map != null) {
          return Order.fromMap(Map<String, dynamic>.from(map));
        }
      } catch (e) {
        handleError(e);
      }
    }
    return null;
  }

  Future<Order?> updateOrder(
      Order order, Position position, UpdateOrderFunction function,
      {Map<String, dynamic>? args}) async {
    if (User.shared.canViewOrder()) {
      try {
        Map<String, dynamic> request = {
          'orderId': order.orderId,
          'recordId': order.recordId,
          'position': position.toJson(),
          'status': order.status,
          'funcName': function.name
        };
        if (args != null) request.addAll(args);
        final result = await FirebaseFunctions.instance
            .httpsCallable('updateOrder')
            .call(request);

        final map = result.data['order'];
        if (map != null) {
          return Order.fromMap(Map<String, dynamic>.from(map));
        }
      } catch (e) {
        handleError(e);
      }
    }
    return null;
  }
}
