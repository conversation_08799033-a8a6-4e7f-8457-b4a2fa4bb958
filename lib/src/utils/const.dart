import 'dart:ui';

import 'package:driver/src/settings/controller.dart';

const Color primaryColor = Color(0xFF029FB0);
const Color backgroundColor = Color(0xFFFAFAFF);
const Color errorColor = Color(0xFFC63B58);
const Color warningColor = Color(0xFFFF752D);

String truckImage(String type,
    {bool isSelected = false, bool ignoreSelection = false}) {
  String img = SettingsController.shared.truckTypes
          .where((e) => e.name == type)
          .firstOrNull
          ?.img ??
      'xslcc';
  return 'assets/images/truck/${ignoreSelection ? '' : (isSelected ? 'select-' : 'unselect-')}$img.png';
}

class OrderStatus {
  static const String finding = '找车中';
  static const String matching = '匹配中';
  static const String pendingConfirm = '待确认车辆';
  static const String pendingContract = '待签合同';
  static const String pendingLoad = '待装货';
  static const String queueing = '排队中';
  static const String inTransit = '运输中';
  static const String inClearance = '清关中';
  static const String pendingUnload = '待卸货';
  static const String completed = '已完成';
  static const String cancelled = '已取消';
}

const List<List<int?>> priceFilters = [
  [null, null],
  [0, 1000],
  [1000, 5000],
  [5000, null]
];

const defaultCustomerServiceNumber = '+77071742905';

const languageOptionsTranslation = {
  'kk': 'қазақ жазуы',
  'ru': 'Русский',
  'zh': '中文',
  'en': 'English'
};

class ActivityReportType {
  static const String dailyReport = '每日报备';
  static const String temporaryStay = '临时休息';
  static const String unexpectedDelay = '特殊延误';
}