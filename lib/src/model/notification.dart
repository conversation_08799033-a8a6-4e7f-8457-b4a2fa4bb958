import 'package:driver/src/utils/firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:driver/src/model/firestore.dart' as model;

class Notification extends ChangeNotifier {
  static final Notification _shared = Notification();
  static Notification get shared => _shared;

  List<model.Notification> notifications = [];

  Future update() async {
    notifications = await Firestore.shared.getNotifications();
    debugPrint('notification updated');
    notifyListeners();
  }

  bool hasUnread() {
    return notifications.any((element) => element.unread);
  }

  Future markRead(model.Notification notification) async {
    if (notification.unread) {
      final updated =
          await Firestore.shared.markNotificationAsRead(notification);
      if (updated != null) {
        notifications[notifications.indexOf(notification)] = updated;
        notifyListeners();
      }
    }
  }

  Future markAllRead() async {
    if (notifications.any((element) => element.unread) &&
        await Firestore.shared.markAllNotificationAsRead()) {
      await update();
    }
  }
}
