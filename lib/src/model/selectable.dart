import 'dart:ui';

import 'package:driver/src/localization/app_localizations.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/cupertino.dart';

mixin Selectable {
  String identifier();
  String label();
}

class LanguageOption with Selectable {
  final Locale locale;

  LanguageOption({required this.locale});

  @override
  String identifier() {
    return locale.languageCode;
  }

  @override
  String label() {
    return languageOptionsTranslation[locale.languageCode]!;
  }

  static List<LanguageOption> allLanguages() {
    return AppLocalizations.supportedLocales
        .map((e) => LanguageOption(locale: e))
        .toList();
  }
}

class ReportItemOption with Selectable {
  final String type;
  final String text;
  final String placeholder;

  ReportItemOption(
      {required this.type, required this.text, required this.placeholder});

  @override
  String identifier() {
    return type;
  }

  @override
  String label() {
    return text;
  }

  static List<ReportItemOption> allReportItemOptions(BuildContext context) {
    final allOptions = [
      {
        'type': ActivityReportType.dailyReport,
        'text': appLoc(context).dailyReport,
        'placeholder': ''
      },
      {
        'type': ActivityReportType.temporaryStay,
        'text': appLoc(context).temporaryStay,
        'placeholder': appLoc(context).temporaryStayPlaceholder
      },
      {
        'type': ActivityReportType.unexpectedDelay,
        'text': appLoc(context).unexpectedDelay,
        'placeholder': appLoc(context).unexpectedDelayPlaceholder
      }
    ];
    return allOptions
        .map((e) => ReportItemOption(
            type: e['type']!, text: e['text']!, placeholder: e['placeholder']!))
        .toList();
  }
}
