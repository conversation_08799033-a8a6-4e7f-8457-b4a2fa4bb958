const { Timestamp } = require("firebase-admin/firestore");
const { getStorage } = require("firebase-admin/storage");
const { HttpsError } = require("firebase-functions/https");
const { sendNotification } = require("./utils.js");

async function createUser(firestore, event) {
  const larkClient = require('./lark-client.js');
  const snapshot = event.data;
  if (!snapshot) {
    console.log("No data associated with the event");
    return;
  }
  const data = snapshot.data();
  const id = event.params.userId;
  const { email, locale, app, os, model, timezone } = data;
  const res = await larkClient.createRecord(larkClient.driverUserTableConfig, {
    '用户编号': id,
    '邮箱': email,
    '语言': locale,
    'APP版本': app,
    '手机系统': os,
    '手机型号': model,
    '时区': timezone
  })
  if (res) {
    const usersRef = firestore.collection('users');
    await usersRef.doc(id).update({
      'sync_at': Timestamp.now()
    })
    console.log(`Sync user ${id}`);
  } else {
    console.log(`Sync user ${id} failed`);
  }
}

async function findDrivers(firestore, phone) {
  // if (!/^(\+7|8|)7[0-9]{9}$/.test(phone)) {
  //   return new HttpsError('invalid-argument', 'Bad Phone Number')
  // }
  // const phoneWithoutAreaCode = phone.substr(phone.length - 10, 10)
  const possiblePhones = [phone, `8${phone}`, `+7${phone}`]
  console.log(possiblePhones);
  const driversRef = firestore.collection('drivers');
  const found = await driversRef.where('phones', 'array-contains-any', possiblePhones).get()
  return found.docs.map(e => e.data())
}

async function findTruck(firestore, license) {
  if (license.length < 7) {
    return new HttpsError('invalid-argument', 'Bad License')
  }

  const trucksRef = firestore.collection('trucks');
  const found = await trucksRef.where('license1', '==', license).get()
  return found.docs.map(e => e.data())
}

async function downloadAttachment(firestore, request, configName, type, db) {
  const larkClient = require('./lark-client.js');
  const { data: { id, token, recordId } } = request;
  if (!id || !token || !recordId) {
    return new HttpsError('invalid-argument', 'Bad Request')
  }

  try {
    const filename = await larkClient.downloadFile(token, recordId,
      larkClient[configName],
      larkClient[configName][`${type}_field_id`]);
    const bucket = getStorage().bucket();
    const destination = `${type}/${id}/${token}.jpg`
    await bucket.upload(filename, {
      destination
    })

    const ref = firestore.collection(db);
    await ref.doc(id).update({
      [`${type}ImgStorage`]: destination
    })

    return { path: destination }
  } catch (err) {
    console.error(err);
    return new HttpsError('internal', 'Download Error')
  }
}

async function deleteDocAndSyncLark(firestore, request) {
  const { data: { id, db, recordId } } = request;
  if (!id || !db) {
    return new HttpsError('invalid-argument', 'Bad Request')
  }

  const larkClient = require('./lark-client.js');
  let config;
  if (db == 'truckVerification') {
    config = larkClient.truckVerificationTableConfig;
  } else if (db == 'bankInfo') {
    config = larkClient.bankTableConfig;
  } else {
    return new HttpsError('invalid-argument', 'Error DB')
  }

  try {
    const ref = firestore.collection(db);
    await ref.doc(id).update({
      'deleted': Timestamp.now()
    })

    const res = await larkClient.updateRecord(config, recordId, {
      '已删除': (new Date()).getTime()
    });
    console.log(`updated ${JSON.stringify(res)}`);

    return { code: 0, status: 'OK' }
  } catch (err) {
    console.error(err);
    return new HttpsError('internal', 'Download Error')
  }
}

async function createBankInfo(firestore, event) {
  const larkClient = require('./lark-client.js');
  const snapshot = event.data;
  if (!snapshot) {
    console.log("No data associated with the event");
    return;
  }
  const id = event.params.docId;
  const { type, name, account, bank, address, swift, bic, bin, kbe, knp, uid } = snapshot.data();
  let fields = {
    '类型': type == 'company' ? '公司账户' : '个体工商户',
    '公司名称/个体工商户姓名': name,
    '开户行名称': bank,
    '账号': account,
    '注册地址': address,
    'SWIFT 代码': swift,
    'BIC': bic,
    'BIN/INN': bin,
    'Kbe': kbe,
    'Knp': knp,
    '用户编号': uid,
    'Firebase记录编号': id
  };
  const res = await larkClient.createRecord(larkClient.bankTableConfig, fields)
  if (res) {
    const { data: { record: { record_id } } } = res;
    const ref = firestore.collection('bankInfo');
    await ref.doc(id).update({
      'recordId': record_id,
      'sync_at': Timestamp.now()
    })
    console.log(`Sync bankInfo ${id}`);
  } else {
    console.log(`Sync bankInfo ${id} failed`);
  }
}

async function createDriverLocation(firestore, event) {
  const larkClient = require('./lark-client.js');
  const snapshot = event.data;
  if (!snapshot) {
    console.log("No data associated with the event");
    return;
  }
  const id = event.params.docId;
  const { uid, position, orderId } = snapshot.data();
  let fields = {
    '司机AppID': uid,
    '订单ID': orderId,
    'Firebase记录编号': id,
    '地理位置': `${position.longitude},${position.latitude}`,
    '海拔': position.altitude,
    '行进方向': position.heading,
    '速度': position.speed,
    '是否模拟': position.isMocked ? '是' : '否'
  };
  console.log(JSON.stringify(fields));
  const res = await larkClient.createRecord(larkClient.driverLocationTableConfig, fields)
  if (res) {
    const { data: { record: { record_id } } } = res;
    const ref = firestore.collection('driverLocation');
    await ref.doc(id).update({
      'recordId': record_id,
      'sync_at': Timestamp.now()
    })
    console.log(`Sync driverLocation ${id}`);
  } else {
    console.log(`Sync driverLocation ${id} failed`);
  }
}

async function updatePayment(firestore, id) {
  const larkClient = require('./lark-client.js');
  const fields = await larkClient.searchRecord(larkClient.driverPaymentTableConfig, id)
  const paymentToken = fields['付款凭证'][0].file_token
  const method = fields['支付方式']
  const uid = fields['司机AppID'].value[0].text
  const driverId = fields['司机编号'].value[0].text
  const orderId = fields['订单编号'].value[0].text
  const amount = fields['金额($)']

  const added = await firestore.collection('payments').add({
    paymentToken,
    method,
    uid,
    driverId,
    orderId,
    amount,
    recordId: id,
    createdAt: Timestamp.now()
  });

  await larkClient.updateRecord(larkClient.driverPaymentTableConfig, id, {
    'Firebase同步时间': (new Date()).getTime()
  });

  await sendNotification(firestore, [uid], 'orderPayment', 'notifications', JSON.stringify({
    id: added.id,
    token: paymentToken,
    recordId: id
  }), (body) => {
    body = body.replace('[method]', `${method}`);
    return body.replace('[amount]', `\$${amount}`)
  });

  return { code: 0, message: 'OK' };
}

async function createActivityReport(firestore, event) {
  const larkClient = require('./lark-client.js');
  const snapshot = event.data;
  if (!snapshot) {
    console.log("No data associated with the event");
    return;
  }
  const id = event.params.docId;
  const { uid, driverId, orderId, location, reason, type } = snapshot.data();
  let fields = {
    '司机AppID': uid,
    '订单编号': orderId,
    'Firebase记录编号': id,
    '位置ID': location,
    '司机编号': driverId,
    '类型': type,
    '报备原因': reason
  };
  console.log(JSON.stringify(fields));
  const res = await larkClient.createRecord(larkClient.driverActivityReportTableConfig, fields)
  if (res) {
    const { data: { record: { record_id } } } = res;
    const ref = firestore.collection('activityReport');
    await ref.doc(id).update({
      'recordId': record_id,
      'sync_at': Timestamp.now()
    })
    console.log(`Sync activityReport ${id}`);
  } else {
    console.log(`Sync activityReport ${id} failed`);
  }
}

module.exports = {
  createUser,
  findDrivers,
  findTruck,
  downloadAttachment,
  deleteDocAndSyncLark,
  createBankInfo,
  createDriverLocation,
  updatePayment,
  createActivityReport
}