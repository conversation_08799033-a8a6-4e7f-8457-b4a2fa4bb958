const { Timestamp } = require('firebase-admin/firestore');

async function genQuotations(firestore, id) {
  const larkClient = require('./lark-client.js');
  const record = await larkClient.searchRecord(larkClient.orderTableConfig, id);
  if (record == null) {
    return {
      'title': `未找到订单`
    }
  }

  const type = record['车辆类型']
  const volumes = record['车载体积'] || []
  const tonnages = record['车载吨位'] || []
  const length = record['车辆长度']
  console.log(type, volumes, tonnages, length);

  const trucksRef = firestore.collection('trucks');
  let query = trucksRef.where('type', '==', type).where('returnTime', '!=', null)
  if (volumes.length > 0) {
    query = query.where('volume', 'in', volumes)
  }
  if (tonnages.length > 0) {
    query = query.where('tonnage', 'in', tonnages)
  }
  if (length != null) {
    query = query.where('length', '==', length)
  }
  const snapshot = await query.orderBy('returnTime').get()
  if (snapshot.size == 0) {
    return {
      'title': `未找到合适的车辆`
    }
  }
  const now = Timestamp.now().toMillis()
  const docs = snapshot.docs.map(doc => doc.data()).filter(
    (value, index, self) =>
      index === self.findIndex((t) => (
        t.driverId === value.driverId
      )) && Math.abs(value['returnTime'].toMillis() - now) < 3 * 24 * 60 * 60 * 1000);
  console.log('found trucks', docs.length);

  const existing = (await larkClient.searchRecords(larkClient.quotationTableConfig, {
    conjunction: 'and',
    conditions: [{
      field_name: '订单编号',
      operator: 'is',
      value: [record['订单编号']]
    }]
  })).map(e => e['fields']['询价编号']);

  let total = existing.length;
  console.log('existing', existing);

  const driversRef = firestore.collection('drivers');
  for (const doc of docs) {
    const driver = await driversRef.doc(doc['driverId']).get()
    const id = `${record['订单编号']}-${driver.data()['nameEn']}`;
    if (!existing.includes(id)) {
      const fields = {
        '询价编号': id,
        '订单编号': record['订单编号'],
        '司机编号': doc.driverId,
        '车辆编号': doc.id
      }
      console.log(fields);
      await larkClient.createRecord(larkClient.quotationTableConfig, fields)
      total++;
    } else {
      console.log(`询价已存在 ${id}`);
    }
  }

  const city = record['目的城市(wx)'] ? record['目的城市(wx)'][0].text : '';
  const pickup = record['取货详细地址'] ? record['取货详细地址'][0].text : '';
  const name = record['下单用户姓名'] ? record['下单用户姓名'].value[0].text : '';

  return {
    'title': `${record['订单编号']}`,
    'link': `https://hcntrphkotcn.feishu.cn/share/base/form/shrcnwHy1Y8jtOnRMdIc9v2ouDz?prefill_%E8%AE%A2%E5%8D%95%E7%BC%96%E5%8F%B7=${record['订单编号']}`,
    'text': `目的地: ${record['目的国家']} ${city}\n起始地: ${record['起始地']} ${pickup}\n下单用户: ${name}\n需求: ${record['车辆类型']} | ${record['车载吨位']} | ${record['车载体积']} | ${record['车辆长度'] ?? ''}\n货物: ${record['货物种类'][0]['text']}\n期望装货时间: ${record['期望装货时间']}\n目标价格: ${record['目标价格']}\n备注: ${record['备注'] ?? ''}\n\n找到 【${total}】 个合适的司机待询价。`
  }
}

module.exports = genQuotations;