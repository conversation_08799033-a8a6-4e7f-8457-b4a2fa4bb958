const lark = require("@larksuiteoapi/node-sdk");
const fs = require('fs')
const path = require('path')

class LarkClient {
  constructor() {
    // https://open.feishu.cn/app/cli_a802c70a1e78d00b/baseinfo
    this.client = new lark.Client({
      appId: 'cli_a802c70a1e78d00b',
      appSecret: 'OuHw4rQ3gvnETywtNaZpnbtXEBQg6Vwa'
    })

    // https://hcntrphkotcn.feishu.cn/base/J6qVbih1daIjAEss9P6cw5O8n2c
    this.appToken = 'Fs6HbbvLTa4760sZ7vmcNL1dnWb'

    this.driverTableConfig = {
      app_token: this.appToken,
      table_id: 'tblv4AHmG6dXz7xq',
      passport_field_id: 'fldxgF3TWC'
    }

    this.truckTableConfig = {
      app_token: this.appToken,
      table_id: 'tblsz6dB9eGyobTJ',
      license1_field_id: 'fldIyH8LFD',
      license2_field_id: 'fldePbXdqw'
    }

    this.orderTableConfig = {
      app_token: this.appToken,
      table_id: 'tblil5XhLMROMwpc',
    }

    this.quotationTableConfig = {
      app_token: this.appToken,
      table_id: 'tbl6JHj2irt9dxgR'
    }

    this.quotationCollectionTableConfig = {
      app_token: this.appToken,
      table_id: 'tbl9r1Kzh1bpGPnh'
    }

    this.driverUserTableConfig = {
      app_token: this.appToken,
      table_id: 'tblSCKOqZyP813rJ'
    }

    this.driverVerificationTableConfig = {
      app_token: this.appToken,
      table_id: 'tbllax9BXrFg47HC'
    }

    this.truckVerificationTableConfig = {
      app_token: this.appToken,
      table_id: 'tblnMfDnxmbn8tjs'
    }

    this.bankTableConfig = {
      app_token: this.appToken,
      table_id: 'tblNYUyJtaLEH9Ue'
    }

    this.orderNegotiationTableConfig = {
      app_token: this.appToken,
      table_id: 'tblyLLNH0zXwTyG3'
    }

    this.driverLocationTableConfig = {
      app_token: this.appToken,
      table_id: 'tblHK3iizbcx8qCT'
    }

    this.driverPaymentTableConfig = {
      app_token: this.appToken,
      table_id: 'tblBDYOffX7ncusR',
      payment_field_id: 'fld8oO0CYQ'
    }

    this.driverActivityReportTableConfig = {
      app_token: this.appToken,
      table_id: 'tbl5UQbEy1AmF1O6'
    }

    // 初始化tenant token
    this.tenantToken = ''
    this.tokenExpireTime = 0
    this.refreshToken()
  }

  /**
* 刷新租户访问令牌
*/
  async refreshToken(force = false) {
    try {
      const now = Math.floor(Date.now() / 1000)

      // 检查token是否仍然有效（保留10分钟缓冲期）
      if (!force && this.tenantToken && now < this.tokenExpireTime - 600) {
        return this.tenantToken
      }

      console.log('正在刷新租户令牌...')
      const res = await this.client.auth.tenantAccessToken.internal({
        data: {
          app_id: this.client.appId,
          app_secret: this.client.appSecret
        }
      })

      this.tenantToken = res.tenant_access_token
      this.tokenExpireTime = now + res.expire

      // 设置提前10分钟刷新（比缓冲期多5分钟）
      const refreshDelay = (res.expire - 600) * 1000
      this.refreshTimer = setTimeout(() => this.refreshToken(), refreshDelay)

      console.log('租户令牌刷新成功，有效期至:', new Date(this.tokenExpireTime * 1000))
      return this.tenantToken
    } catch (err) {
      console.error('刷新租户令牌失败:', err)
      // 30秒后重试
      setTimeout(() => this.refreshToken(), 30000)
    }
  }

  async searchRecord(config, recordId) {
    await this.refreshToken()
    try {
      const { data: { records } } = await this.client.bitable.v1.appTableRecord.batchGet(
        {
          path: config,
          data: {
            record_ids: [recordId],
          },
        },
        lark.withTenantToken(this.tenantToken)
      );
      if (records?.length > 0) {
        return records[0].fields
      }
    } catch (err) {
      console.error('检查失败:', err)
    }

    return null
  }

  async searchRecords(config, filter, pageSize = 20, max = 500) {
    await this.refreshToken()
    try {
      let result = []
      for await (const item of await this.client.bitable.v1.appTableRecord.searchWithIterator({
        path: config,
        params: {
          page_size: pageSize,
        },
        data: {
          filter
        }
      },
        lark.withTenantToken(this.tenantToken)
      )) {
        if (item.items) {
          result.push(...(item.items));
        } else {
          console.log(`item ${item}`);
        }

        if (result.length >= max) {
          break
        }
      }

      return result
    } catch (err) {
      console.error('检查失败:', err)
      return []
    }
  }

  async createRecord(config, fields) {
    await this.refreshToken()
    try {
      const res = await this.client.bitable.v1.appTableRecord.create({
        path: config,
        data: {
          fields
        }
      },
        lark.withTenantToken(this.tenantToken)
      )

      console.log(`createRecord: ${JSON.stringify(res)}`);
      return res
    } catch (err) {
      console.log(`createRecord ${err}`);
      return null
    }
  }

  async updateRecord(config, recordId, fields) {
    await this.refreshToken()
    try {
      const res = await this.client.bitable.v1.appTableRecord.update({
        path: {
          ...config,
          record_id: recordId
        },
        data: {
          fields
        }
      },
        lark.withTenantToken(this.tenantToken)
      )

      console.log(`updateRecord: ${JSON.stringify(res)}`);
      return res
    } catch (err) {
      console.log(`更新失败: ${err}`);
      return null
    }
  }

  async downloadFile(token, recordId, config, field_id) {
    await this.refreshToken()
    try {
      const res = await this.client.drive.v1.media.download({
        path: {
          file_token: token,
        },
        params: {
          extra: `{"bitablePerm":{"tableId":"${config.table_id}","attachments":{"${field_id}":{"${recordId}":["${token}"]}}}}`,
        }
      },
        lark.withTenantToken(this.tenantToken)
      )
      const filename = `/tmp/${recordId}.${(new Date()).getTime()}`
      await res.writeFile(filename)
      return filename
    } catch (err) {
      console.error('下载图片失败:', err)
      throw err
    }
  }

  async uploadFile(config, file) {
    await this.refreshToken()

    const name = path.basename(file);
    const size = fs.statSync(file).size
    try {
      const res = await this.client.drive.v1.media.uploadAll({
        data: {
          file_name: name,
          parent_type: 'bitable_image',
          parent_node: config.app_token,
          size,
          file: fs.createReadStream(file)
        }
      },
        lark.withTenantToken(this.tenantToken)
      )

      return {
        token: res.file_token,
        name, size
      }
    } catch (err) {
      console.error('上传图片失败:', err)
      throw err
    }
  }
}

module.exports = new LarkClient();
