const { Timestamp, getFirestore } = require("firebase-admin/firestore");
const { syncDriver, syncTruck, sendNotification } = require("./utils");
require("firebase-functions/logger/compat");

async function syncDrivers(firestore, larkClient) {
  const drivers = await larkClient.searchRecords(larkClient.driverTableConfig, {
    conjunction: 'and',
    conditions: [{
      field_name: 'Firebase同步时间',
      operator: 'isEmpty',
      value: []
    }]
  }, 100)

  console.log(`unsynced drivers: ${drivers.length}`);

  if (drivers.length == 0) {
    return;
  }

  const driversRef = firestore.collection('drivers');

  for (const driver of drivers) {
    await syncDriver(driver, driversRef, larkClient);
  }
}

async function syncTrucks(firestore, larkClient) {
  const trucks = await larkClient.searchRecords(larkClient.truckTableConfig, {
    conjunction: 'and',
    conditions: [{
      field_name: 'Firebase同步时间',
      operator: 'isEmpty',
      value: []
    }]
  }, 100)

  console.log(`unsynced trucks: ${trucks.length}`);

  if (trucks.length == 0) {
    return;
  }

  const trucksRef = firestore.collection('trucks');

  for (const truck of trucks) {
    await syncTruck(truck, trucksRef, larkClient);
  }
}

async function syncQuotations(firestore, larkClient) {
  const quotations = await larkClient.searchRecords(larkClient.quotationCollectionTableConfig, {
    conjunction: 'and',
    conditions: [{
      field_name: 'Firebase同步时间',
      operator: 'isEmpty',
      value: []
    }]
  }, 100)

  console.log(`unsynced quotations: ${quotations.length}`);

  if (quotations.length == 0) {
    return;
  }

  const firestore1 = getFirestore();
  const quotationsRef = firestore1.collection('quotationCollections');

  for (const quo of quotations) {
    const { fields, record_id } = quo
    const id = fields['编号']
    let destination = fields['目的地']
    if (destination == null) {
      console.log('===== destination is null, skipping');
      continue;
    }
    if (destination == '其他-详细备注' && fields['详细目的地'] != null) {
      destination = fields['详细目的地'][0].text
    }
    const typeName = fields['车型']
    const quotation = fields['纯运费']
    const created = fields['创建时间']
    let arr = typeName.split('-')
    const type = arr[0].trim()
    let length = null
    let volume = null
    if (arr.length > 1) {
      if (arr[1].includes('米')) {
        length = parseInt(arr[1].replace('米', ''))
      } else if (arr[1].includes('m³')) {
        volume = parseInt(arr[1].replace('m³', ''))
      }
    }
    console.log(id, destination, type, quotation, length, volume, created);
    const prev = await quotationsRef.where('destination', '==', destination)
      .where('typeName', '==', typeName)
      .where('created', '<', Timestamp.fromMillis(created))
      .orderBy('updated', 'desc').limit(1).get()
    let delta = 0
    if (!prev.empty) {
      const prevQuotation = prev.docs[0].data()['quotation'];
      delta = (quotation - prevQuotation) / prevQuotation;
    } else {
      console.log('===== prev empty');
    }

    await quotationsRef.doc(id).set({
      destination,
      typeName,
      type,
      length,
      volume,
      id,
      quotation,
      recordId: record_id,
      delta,
      updated: Timestamp.now(),
      created: Timestamp.fromMillis(created)
    })
    console.log(`synced quotation ${id}`);
    const res = await larkClient.updateRecord(larkClient.quotationCollectionTableConfig, record_id, {
      'Firebase同步时间': (new Date()).getTime()
    });
    console.log(`updated quotation ${JSON.stringify(res)}`);
  }
}

async function checkAndNotifyUnreportedDrivers(firestore) {
  const onGoingStatus = ['待装货', '排队中', '运输中', '清关中', '待卸货'];
  // find all orders with onGoingStatus
  const ref = firestore.collection('orders').where('status', 'in', onGoingStatus);
  const docs = await ref.get();
  const uids = new Set();
  const orderIds = docs.docs.map(doc => {
    if (doc.data()['uid'] != null && doc.data()['uid'] != '') {
      uids.add(doc.data()['uid']);
    }
    return doc.id;
  });
  console.log(`found ${orderIds.length} orders`);
  const twentyFourHoursAgo = new Date();
  twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

  if (orderIds.length > 0) {
    // remove orders that have been reported in the last 24 hours
    const locationRef = firestore.collection('driverLocation')
      .where('orderId', 'in', orderIds)
      .where('createdAt', '>', twentyFourHoursAgo);
    const locations = await locationRef.get();
    console.log(`found ${locations.docs.length} locations`);
    locations.docs.forEach(doc => {
      uids.delete(doc.data()['uid']);
    });
  }
  if (uids.size == 0) {
    console.log('no unreported drivers');
    return;
  }

  // remove uids that have been notified in the last 24 hours
  const notificationQuery = firestore.collection('positionReportNotifications')
    .where('uid', 'in', [...uids])
    .where('updated', '>', twentyFourHoursAgo);
  console.log(`querying notifications for ${[...uids]} updated > ${twentyFourHoursAgo}`);
  const notifications = await notificationQuery.get();
  console.log(`found ${notifications.docs.length} notifications`);
  notifications.docs.forEach(doc => {
    uids.delete(doc.data()['uid']);
  });
  if (uids.size == 0) {
    console.log('no unreported drivers');
    return;
  }

  // send positionReport notifications to these uids
  console.log(`send positionReport notification to ${[...uids]}`);
  await sendNotification(firestore, [...uids], 'positionReport');
  // update positionReportNotifications collection
  for (const uid of uids) {
    await firestore.collection('positionReportNotifications').doc(uid).set({
      uid,
      updated: Timestamp.now()
    }, { merge: true });
  }
}

module.exports = {
  syncDrivers,
  syncTrucks,
  syncQuotations,
  checkAndNotifyUnreportedDrivers
}