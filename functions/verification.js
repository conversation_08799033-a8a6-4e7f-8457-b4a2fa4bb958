const { Timestamp } = require('firebase-admin/firestore');
const { syncDriver, syncTruck, sendNotification, uploadImageToLark } = require('./utils.js');

async function createDriverVerification(firestore, event) {
  const larkClient = require('./lark-client.js');
  const snapshot = event.data;
  if (!snapshot) {
    console.log("No data associated with the event");
    return;
  }
  const id = event.params.userId;
  const { name, passport, phone, driverId, chinesePhone, wechat } = snapshot.data();
  let fields = {
    '关联司机': driverId,
    '用户输入姓名': name,
    '用户输入电话': phone,
    '用户输入中国电话': chinesePhone,
    '用户输入微信号': wechat,
    '用户编号': id
  };
  let passportAttachment;
  if (passport?.length > 0 && !(driverId?.length > 0 && passport.includes(`passport/${driverId}`))) {
    // upload passport to lark
    passportAttachment = await uploadImageToLark(passport, larkClient.driverVerificationTableConfig, larkClient);
  }
  if (passportAttachment != null) {
    fields['用户上传护照'] = [passportAttachment];
  }
  const res = await larkClient.createRecord(larkClient.driverVerificationTableConfig, fields)
  if (res) {
    const ref = firestore.collection('driverVerification');
    await ref.doc(id).update({
      'sync_at': Timestamp.now()
    })
    console.log(`Sync driverVerification ${id}`);
  } else {
    console.log(`Sync driverVerification ${id} failed`);
  }
}

async function updateDriverVerification(firestore, recordId) {
  const larkClient = require('./lark-client.js');
  const fields = await larkClient.searchRecord(larkClient.driverVerificationTableConfig, recordId);
  const status = fields['状态']['value'][0].text;
  const driverId = fields['关联司机'] != null ? fields['关联司机'][0].text : '';
  const uid = fields['用户编号'][0].text;
  const ref = firestore.collection('driverVerification').doc(uid);
  let notificationType;
  let routeName;
  console.log(`updateDriverVerification ${recordId} ${status} ${driverId} ${uid}`);
  if (status == '已验证') {
    const drivers = await larkClient.searchRecords(larkClient.driverTableConfig, {
      conjunction: 'and',
      conditions: [{
        field_name: '司机编号',
        operator: 'is',
        value: [driverId.replace('SJ', '')]
      }]
    }, 1);

    if (drivers.length > 0) {
      const { nameEn, id, foreignPhone, chinesePhone, wechat }
        = await syncDriver(drivers[0], firestore.collection('drivers'), larkClient);
      await ref.set({
        status: 'verified',
        chinesePhone,
        driverId: id,
        recordId,
        name: nameEn,
        phone: foreignPhone,
        wechat,
        updatedAt: Timestamp.now()
      }, { merge: true });
    }
    notificationType = 'driverVerified';
  } else if (status == '验证失败') {
    const reason = fields['验证失败备注'] ?? '';
    await ref.set({
      status: 'rejected',
      recordId,
      reason,
      updatedAt: Timestamp.now()
    }, { merge: true });
    notificationType = 'driverRejected';
    routeName = 'driver_info';
  } else {
    return { code: -1, message: 'Invalid status' };
  }

  await larkClient.updateRecord(larkClient.driverVerificationTableConfig, recordId, {
    'Firebase同步时间': (new Date()).getTime()
  });

  await sendNotification(firestore, [uid], notificationType, routeName);

  return { code: 0, message: 'OK' };
}

async function createTruckVerification(firestore, event) {
  const larkClient = require('./lark-client.js');
  const snapshot = event.data;
  if (!snapshot) {
    console.log("No data associated with the event");
    return;
  }
  const id = event.params.docId;
  const { license, licenseStorage, tonnage, truckId, type, volume, uid } = snapshot.data();
  let fields = {
    '关联车辆': truckId,
    '用户输入车牌号': license,
    '用户输入车辆类型': type,
    '用户输入车载吨位': tonnage,
    '用户输入车载体积': volume,
    '用户编号': uid,
    'Firebase记录编号': id
  };
  let licenseAttachment;
  if (licenseStorage?.length > 0 && !(truckId?.length > 0 && licenseStorage.includes(`license1/${truckId}`))) {
    // upload license to lark
    licenseAttachment = await uploadImageToLark(licenseStorage, larkClient.truckVerificationTableConfig, larkClient);
  }
  if (licenseAttachment != null) {
    fields['用户上传行驶证'] = [licenseAttachment];
  }
  const res = await larkClient.createRecord(larkClient.truckVerificationTableConfig, fields)
  if (res) {
    const ref = firestore.collection('truckVerification');
    await ref.doc(id).update({
      'sync_at': Timestamp.now()
    })
    console.log(`Sync truckVerification ${id}`);
  } else {
    console.log(`Sync truckVerification ${id} failed`);
  }
}

async function updateTruckVerification(firestore, recordId) {
  const larkClient = require('./lark-client.js');
  const fields = await larkClient.searchRecord(larkClient.truckVerificationTableConfig, recordId);
  const status = fields['状态']['value'][0].text;
  const truckId = fields['关联车辆'] != null ? fields['关联车辆'][0].text : '';
  const docId = fields['Firebase记录编号'][0].text;
  const uid = fields['用户编号'][0].text;
  const ref = firestore.collection('truckVerification').doc(docId);
  let notificationType;
  console.log(`updateTruckVerification ${recordId} ${status} ${truckId} ${docId} ${uid}`);
  if (status == '已验证') {
    const trucks = await larkClient.searchRecords(larkClient.truckTableConfig, {
      conjunction: 'and',
      conditions: [{
        field_name: '车辆编号',
        operator: 'is',
        value: [truckId.replace('CL', '')]
      }]
    }, 1);

    if (trucks.length > 0) {
      const { id, license1, tonnage, type, volume }
        = await syncTruck(trucks[0], firestore.collection('trucks'), larkClient);
      await ref.set({
        status: 'verified',
        license: license1,
        truckId: id,
        recordId,
        tonnage,
        type,
        volume,
        updatedAt: Timestamp.now()
      }, { merge: true });
    }
    notificationType = 'truckVerified';
  } else if (status == '验证失败') {
    const reason = fields['验证失败备注'] ?? '';
    await ref.set({
      status: 'rejected',
      recordId,
      reason,
      updatedAt: Timestamp.now()
    }, { merge: true });
    notificationType = 'truckRejected';
  } else {
    return { code: -1, message: 'Invalid status' };
  }

  await larkClient.updateRecord(larkClient.truckVerificationTableConfig, recordId, {
    'Firebase同步时间': (new Date()).getTime()
  });

  await sendNotification(firestore, [uid], notificationType, 'truck_management');

  return { code: 0, message: 'OK' };
}

module.exports = {
  createDriverVerification,
  updateDriverVerification,
  createTruckVerification,
  updateTruckVerification
}