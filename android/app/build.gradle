plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    // For Local
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
} else {
    // For CI
    if (System.getenv('KEY_STORE_PASSWORD')) {
        keystoreProperties.setProperty('storePassword', System.getenv('KEY_STORE_PASSWORD'))
    }
    if (System.getenv('KEY_PASSWORD')) {
        keystoreProperties.setProperty('keyPassword', System.getenv('KEY_PASSWORD'))
    }
    if (System.getenv('ALIAS')) {
        keystoreProperties.setProperty('keyAlias', System.getenv('ALIAS'))
    }
    if (System.getenv('KEY_PATH')) {
        keystoreProperties.setProperty('storeFile', System.getenv('KEY_PATH'))
    }
}

android {
    namespace = "com.mashina.driver"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.mashina.driver"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 26
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}
