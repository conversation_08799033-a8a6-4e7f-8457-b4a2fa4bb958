package com.mashina.driver

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private var androidAppRetainChannel: MethodChannel? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        androidAppRetainChannel = MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            "android_app_retain"
        )
        androidAppRetainChannel!!.setMethodCallHandler { call: MethodCall?, result: MethodChannel.Result? ->
            if (call!!.method == "sendToBackground") {
                moveTaskToBack(true)
                result!!.success("")
            }
        }
    }

    override fun cleanUpFlutterEngine(flutterEngine: FlutterEngine) {
        androidAppRetainChannel = null
        super.cleanUpFlutterEngine(flutterEngine)
    }
}
